# Spring Boot 应用测试生成指南

本文档介绍了 JSP 到 Spring Boot 迁移工具的自动化测试生成功能。

## 概述

测试生成器为生成的 Spring Boot 应用创建全面的测试套件，包括：

1. **单元测试** - 控制器、服务和仓库层的单元测试
2. **集成测试** - 完整的 API 端到端测试
3. **测试数据** - 测试数据构建器和固定装置
4. **测试配置** - 测试环境配置

## 生成的测试类型

### 1. 控制器测试 (Controller Tests)

使用 `@WebMvcTest` 和 MockMvc 进行控制器单元测试：

```java
@WebMvcTest(PostController.class)
class PostControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private PostService postService;
    
    @Test
    void testGetAllPosts() throws Exception {
        // Given
        List<Post> posts = Arrays.asList(new Post(), new Post());
        when(postService.findAll()).thenReturn(posts);
        
        // When & Then
        mockMvc.perform(get("/api/v1/posts"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
}
```

**特性：**
- 完整的 CRUD 操作测试
- JSON 序列化/反序列化测试
- HTTP 状态码验证
- 服务层模拟

### 2. 服务测试 (Service Tests)

使用 Mockito 进行服务层单元测试：

```java
@ExtendWith(MockitoExtension.class)
class PostServiceTest {
    
    @InjectMocks
    private PostService postService;
    
    @Mock
    private PostRepository postRepository;
    
    @Test
    void testFindAll() {
        // Given
        List<Post> posts = Arrays.asList(new Post(), new Post());
        when(postRepository.findAll()).thenReturn(posts);
        
        // When
        List<Post> result = postService.findAll();
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(postRepository).findAll();
    }
}
```

**特性：**
- 业务逻辑验证
- 仓库层模拟
- 异常处理测试
- 事务行为测试

### 3. 仓库测试 (Repository Tests)

使用 `@DataJpaTest` 进行数据访问层测试：

```java
@DataJpaTest
@ActiveProfiles("test")
class PostRepositoryTest {
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    void testSaveAndFindById() {
        // Given
        Post post = new Post();
        
        // When
        Post savedPost = postRepository.save(post);
        entityManager.flush();
        entityManager.clear();
        
        Optional<Post> foundPost = postRepository.findById(savedPost.getId());
        
        // Then
        assertTrue(foundPost.isPresent());
        assertEquals(savedPost.getId(), foundPost.get().getId());
    }
}
```

**特性：**
- JPA 实体映射验证
- 数据库约束测试
- 查询方法测试
- 事务回滚测试

### 4. 集成测试 (Integration Tests)

使用 `@SpringBootTest` 和 TestRestTemplate 进行端到端测试：

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class ApiIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @LocalServerPort
    private int port;
    
    @Test
    void testPostCrudWorkflow() {
        // 1. Create a new post
        Post newPost = new Post();
        ResponseEntity<Post> createResponse = restTemplate.postForEntity(
            createUrl("/posts"), new HttpEntity<>(newPost, createHeaders()), Post.class);
        
        assertEquals(HttpStatus.CREATED, createResponse.getStatusCode());
        
        // 2. Get all posts
        ResponseEntity<Post[]> getAllResponse = restTemplate.getForEntity(
            createUrl("/posts"), Post[].class);
        
        assertEquals(HttpStatus.OK, getAllResponse.getStatusCode());
        assertTrue(getAllResponse.getBody().length > 0);
    }
}
```

**特性：**
- 完整的应用上下文加载
- 真实的 HTTP 请求/响应
- 数据库集成测试
- 工作流验证

### 5. 测试数据生成

#### 测试数据构建器 (Test Data Builders)

```java
public class PostTestDataBuilder {
    private Post entity;
    
    public PostTestDataBuilder() {
        this.entity = new Post();
        withDefaultValues();
    }
    
    public static PostTestDataBuilder aPost() {
        return new PostTestDataBuilder();
    }
    
    public PostTestDataBuilder withTitle(String title) {
        // entity.setTitle(title);
        return this;
    }
    
    public Post build() {
        return entity;
    }
}
```

#### 测试固定装置 (Test Fixtures)

```java
public final class PostTestFixture {
    
    private PostTestFixture() {
        // Utility class
    }
    
    public static Post defaultPost() {
        return PostTestDataBuilder.aPost().build();
    }
    
    public static List<Post> multiplePosts(int count) {
        List<Post> entities = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            entities.add(PostTestDataBuilder.aPost().withId((long) (i + 1)).build());
        }
        return entities;
    }
}
```

## 测试配置

### 测试应用配置 (application-test.properties)

```properties
# Test Application Configuration
spring.application.name=migrated-app-test

# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Test Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Test Profile
spring.profiles.active=test
```

### 测试依赖 (build.gradle)

生成的项目包含以下测试依赖：

```gradle
dependencies {
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.8.0'
    testImplementation 'org.assertj:assertj-core:3.25.1'
    testImplementation 'com.fasterxml.jackson.core:jackson-databind'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:h2'
}
```

## 运行测试

### 运行所有测试

```bash
./gradlew test
```

### 运行特定类型的测试

```bash
# 只运行单元测试
./gradlew test --tests "*Test" --exclude-tests "*IntegrationTest"

# 只运行集成测试
./gradlew test --tests "*IntegrationTest"

# 运行特定包的测试
./gradlew test --tests "com.example.migrated.controller.*"
```

### 生成测试报告

```bash
./gradlew test jacocoTestReport
```

测试报告将生成在 `build/reports/tests/test/index.html`

## 测试最佳实践

1. **测试隔离** - 每个测试都应该独立运行
2. **数据清理** - 使用 `@Transactional` 或 `@DirtiesContext` 确保测试数据不会影响其他测试
3. **模拟外部依赖** - 使用 `@MockBean` 模拟外部服务
4. **测试数据** - 使用测试数据构建器创建一致的测试数据
5. **断言清晰** - 使用描述性的断言消息

## 自定义测试生成

如果需要自定义测试生成，可以：

1. 修改 `TestSuiteGenerator` 配置
2. 扩展测试数据构建器
3. 添加自定义测试注解
4. 配置特定的测试场景

## 故障排除

### 常见问题

1. **测试数据库连接失败** - 检查 H2 数据库配置
2. **模拟对象未注入** - 确保使用正确的 Mockito 注解
3. **JSON 序列化错误** - 检查实体类的 Jackson 注解
4. **测试上下文加载失败** - 检查 Spring Boot 配置

### 调试技巧

1. 启用 SQL 日志：`spring.jpa.show-sql=true`
2. 使用 `@Sql` 注解预加载测试数据
3. 使用 `@TestPropertySource` 覆盖配置
4. 使用 `@MockBean` 的 `reset` 方法清理模拟状态
