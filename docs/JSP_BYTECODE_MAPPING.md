# JSP 到字节码映射功能

## 概述

本文档描述了 JSP 到 Spring Boot 迁移工具中新增的 JSP 文件与编译后字节码之间的映射功能。该功能使用 JavaParser 解析 JSP 文件，使用 ASM 分析编译后的字节码，并建立两者之间的映射关系。

## 功能特性

### 1. 核心映射功能
- **JSP 文件到 Servlet 类的映射**：自动识别 JSP 文件对应的编译后 Servlet 类
- **类名映射**：建立 JSP 中引用的类与字节码中实际类的对应关系
- **方法映射**：映射 JSP 中的方法声明与字节码中的方法实现
- **依赖关系映射**：关联 JSP 依赖与字节码依赖
- **标签库映射**：记录 JSP 使用的标签库信息

### 2. 智能匹配算法
- **多种命名约定支持**：支持 Tomcat、WebLogic 等不同容器的 Servlet 命名规则
- **模糊匹配**：当精确匹配失败时，使用模糊匹配算法
- **置信度评分**：为每个映射计算置信度分数，基于命名相似性和依赖重叠度

### 3. 映射提示提取
- **JSP 元数据提取**：从 JSP 页面指令中提取扩展类、实现接口等信息
- **方法签名识别**：从 JSP 声明和脚本片段中提取方法签名
- **变量声明分析**：识别 JSP 中的变量声明

## 架构设计

### 核心类结构

```
com.phodal.legacy.mapper/
├── JspBytecodeMapper.java          # 主映射器类
└── 

com.phodal.legacy.model/
├── JspBytecodeMapping.java         # 映射关系数据模型
├── MappingRegistry.java            # 映射注册表
└── ComponentModel.java             # 组件模型（扩展）

com.phodal.legacy.parser/
├── JspAnalyzer.java                # JSP 分析器（扩展）
└── BytecodeAnalyzer.java           # 字节码分析器（扩展）

com.phodal.legacy.services/
└── AnalysisService.java            # 分析服务（集成映射功能）
```

### 数据模型

#### JspBytecodeMapping
```java
public class JspBytecodeMapping {
    private String jspComponentId;              // JSP 组件 ID
    private String jspPath;                     // JSP 文件路径
    private String compiledServletClass;        // 编译后的 Servlet 类名
    private Map<String, String> classNameMappings;      // 类名映射
    private Map<String, MethodMapping> methodMappings;  // 方法映射
    private Map<String, String> dependencyMappings;     // 依赖映射
    private Set<String> tagLibraryMappings;     // 标签库映射
    private Map<String, Object> metadata;       // 元数据
}
```

#### MappingRegistry
```java
public class MappingRegistry {
    private Map<String, JspBytecodeMapping> jspMappings;        // 主映射存储
    private Map<String, Set<String>> classToJspMappings;       // 反向映射：类到JSP
    private Map<String, Set<String>> dependencyMappings;       // 反向映射：依赖到JSP
}
```

## 使用方法

### 1. 基本使用

```java
// 创建映射器
JspBytecodeMapper mapper = new JspBytecodeMapper();

// 获取 JSP 和字节码组件
List<ComponentModel> jspComponents = jspAnalyzer.analyzeJspFiles(projectRoot);
List<ComponentModel> bytecodeComponents = bytecodeAnalyzer.analyzeBytecode(projectRoot);

// 创建映射
MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);

// 查询映射
JspBytecodeMapping mapping = registry.getMapping("jsp1");
```

### 2. 集成到分析服务

```java
// 在 AnalysisService 中自动创建映射
AnalysisService service = new AnalysisService();
AnalysisService.AnalysisResult result = service.analyzeProject(projectRoot, options);

// 获取映射注册表
MappingRegistry mappingRegistry = result.getMappingRegistry();

// 获取映射统计
MappingRegistry.MappingStatistics stats = mappingRegistry.getStatistics();
```

### 3. 映射查询

```java
// 按 JSP 路径查询
JspBytecodeMapping mapping = registry.getMappingByPath("/webapp/index.jsp");

// 按字节码类查询
Set<JspBytecodeMapping> mappings = registry.getMappingsByBytecodeClass("org.apache.jsp.index_jsp");

// 按依赖查询
Set<JspBytecodeMapping> mappings = registry.getMappingsByDependency("java.util.List");

// 获取统计信息
MappingRegistry.MappingStatistics stats = registry.getStatistics();
```

## 支持的 Servlet 命名约定

映射器支持多种常见的 JSP 到 Servlet 编译命名约定：

1. **Tomcat 风格**：`index.jsp` → `index_jsp`
2. **WebLogic 风格**：`index.jsp` → `indexJSP`
3. **通用风格**：`index.jsp` → `index$jsp`
4. **包装风格**：`index.jsp` → `jsp.index_jsp`
5. **Jasper 风格**：`index.jsp` → `_jsp._index_jsp`

## 映射置信度计算

映射置信度基于以下因素计算：

- **基础置信度**：0.5
- **命名相似性**：+0.3（如果 Servlet 类名包含 JSP 名称）
- **依赖重叠度**：+0.2 × (共同依赖数 / JSP 依赖总数)
- **最大置信度**：1.0

## 测试覆盖

### 单元测试
- `JspBytecodeMapperTest`：映射器核心功能测试
- `MappingRegistryTest`：映射注册表功能测试

### 集成测试
- `JspBytecodeMappingIntegrationTest`：端到端映射流程测试

### 示例测试
- `MappingExampleTest`：完整功能演示

## 性能考虑

1. **并发安全**：MappingRegistry 使用 ConcurrentHashMap 确保线程安全
2. **内存优化**：使用反向映射索引提高查询效率
3. **延迟计算**：统计信息按需计算

## 扩展点

1. **自定义命名约定**：可以扩展 `SERVLET_NAME_PATTERNS` 支持更多命名规则
2. **映射提示扩展**：可以在 `extractMappingHints` 中添加更多提取逻辑
3. **置信度算法**：可以自定义 `calculateMappingConfidence` 方法

## 限制和注意事项

1. **依赖编译后的字节码**：需要项目已经编译生成 class 文件或 JAR 包
2. **命名约定依赖**：映射准确性依赖于 Servlet 容器的命名约定
3. **复杂 JSP 支持**：对于使用动态包含或复杂标签的 JSP，映射可能不够精确

## 未来改进

1. **动态分析**：结合运行时信息提高映射准确性
2. **机器学习**：使用 ML 算法改进映射匹配
3. **可视化**：提供映射关系的图形化展示
4. **增量更新**：支持项目变更时的增量映射更新
