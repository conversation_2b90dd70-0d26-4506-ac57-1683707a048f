# 生成的 Spring Boot 测试示例

本示例展示了 JSP 到 Spring Boot 迁移工具生成的测试代码结构和内容。

## 项目结构

```
generated-spring-boot-app/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/migrated/
│   │   │       ├── Application.java
│   │   │       ├── controller/
│   │   │       │   ├── PostController.java
│   │   │       │   └── UserController.java
│   │   │       ├── service/
│   │   │       │   ├── PostService.java
│   │   │       │   └── UserService.java
│   │   │       ├── repository/
│   │   │       │   ├── PostRepository.java
│   │   │       │   └── UserRepository.java
│   │   │       └── entity/
│   │   │           ├── Post.java
│   │   │           └── User.java
│   │   └── resources/
│   │       ├── application.properties
│   │       └── application.yml
│   └── test/
│       ├── java/
│       │   └── com/example/migrated/
│       │       ├── controller/
│       │       │   ├── PostControllerTest.java
│       │       │   └── UserControllerTest.java
│       │       ├── service/
│       │       │   ├── PostServiceTest.java
│       │       │   └── UserServiceTest.java
│       │       ├── repository/
│       │       │   ├── PostRepositoryTest.java
│       │       │   └── UserRepositoryTest.java
│       │       ├── integration/
│       │       │   ├── ApiIntegrationTest.java
│       │       │   ├── PostControllerIntegrationTest.java
│       │       │   └── UserControllerIntegrationTest.java
│       │       ├── testdata/
│       │       │   ├── PostTestDataBuilder.java
│       │       │   ├── PostTestFixture.java
│       │       │   ├── UserTestDataBuilder.java
│       │       │   ├── UserTestFixture.java
│       │       │   └── TestDataFactory.java
│       │       └── config/
│       │           └── TestConfiguration.java
│       └── resources/
│           └── application-test.properties
├── build.gradle
└── README.md
```

## 测试类型说明

### 1. 控制器单元测试

**PostControllerTest.java** - 使用 MockMvc 测试 REST API 端点：

```java
@WebMvcTest(PostController.class)
class PostControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private PostService postService;
    
    @Test
    void testGetAllPosts() throws Exception {
        // 测试 GET /api/v1/posts
        mockMvc.perform(get("/api/v1/posts"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
    
    @Test
    void testCreatePost() throws Exception {
        // 测试 POST /api/v1/posts
        Post post = new Post();
        mockMvc.perform(post("/api/v1/posts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(post)))
            .andExpect(status().isCreated());
    }
}
```

### 2. 服务层单元测试

**PostServiceTest.java** - 使用 Mockito 测试业务逻辑：

```java
@ExtendWith(MockitoExtension.class)
class PostServiceTest {
    
    @InjectMocks
    private PostService postService;
    
    @Mock
    private PostRepository postRepository;
    
    @Test
    void testFindAll() {
        // Given
        List<Post> posts = Arrays.asList(new Post(), new Post());
        when(postRepository.findAll()).thenReturn(posts);
        
        // When
        List<Post> result = postService.findAll();
        
        // Then
        assertEquals(2, result.size());
        verify(postRepository).findAll();
    }
}
```

### 3. 仓库层集成测试

**PostRepositoryTest.java** - 使用 @DataJpaTest 测试数据访问：

```java
@DataJpaTest
@ActiveProfiles("test")
class PostRepositoryTest {
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    void testSaveAndFindById() {
        // Given
        Post post = new Post();
        
        // When
        Post savedPost = postRepository.save(post);
        entityManager.flush();
        
        // Then
        Optional<Post> found = postRepository.findById(savedPost.getId());
        assertTrue(found.isPresent());
    }
}
```

### 4. API 集成测试

**ApiIntegrationTest.java** - 使用 TestRestTemplate 测试完整工作流：

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class ApiIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @LocalServerPort
    private int port;
    
    @Test
    void testPostCrudWorkflow() {
        // 1. 创建文章
        Post newPost = new Post();
        ResponseEntity<Post> createResponse = restTemplate.postForEntity(
            createUrl("/posts"), new HttpEntity<>(newPost), Post.class);
        assertEquals(HttpStatus.CREATED, createResponse.getStatusCode());
        
        // 2. 获取所有文章
        ResponseEntity<Post[]> getAllResponse = restTemplate.getForEntity(
            createUrl("/posts"), Post[].class);
        assertEquals(HttpStatus.OK, getAllResponse.getStatusCode());
    }
}
```

### 5. 测试数据构建器

**PostTestDataBuilder.java** - 构建器模式创建测试数据：

```java
public class PostTestDataBuilder {
    private Post entity;
    
    public PostTestDataBuilder() {
        this.entity = new Post();
        withDefaultValues();
    }
    
    public static PostTestDataBuilder aPost() {
        return new PostTestDataBuilder();
    }
    
    public PostTestDataBuilder withTitle(String title) {
        // entity.setTitle(title);
        return this;
    }
    
    public Post build() {
        return entity;
    }
}
```

## 运行测试

### 运行所有测试
```bash
./gradlew test
```

### 运行特定类型的测试
```bash
# 单元测试
./gradlew test --tests "*Test" --exclude-tests "*IntegrationTest"

# 集成测试
./gradlew test --tests "*IntegrationTest"

# 控制器测试
./gradlew test --tests "*ControllerTest"
```

### 生成测试报告
```bash
./gradlew test jacocoTestReport
```

## 测试配置

### application-test.properties
```properties
# 测试数据库配置
spring.datasource.url=jdbc:h2:mem:testdb
spring.jpa.hibernate.ddl-auto=create-drop
spring.profiles.active=test
```

### build.gradle 测试依赖
```gradle
dependencies {
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.assertj:assertj-core'
}
```

## 测试覆盖率

生成的测试套件提供了全面的测试覆盖：

- **控制器层**: HTTP 端点、状态码、JSON 序列化
- **服务层**: 业务逻辑、异常处理、事务管理
- **仓库层**: 数据持久化、查询方法、约束验证
- **集成测试**: 端到端工作流、组件交互

## 自定义和扩展

1. **添加自定义断言**: 扩展测试方法添加业务特定的验证
2. **测试数据**: 修改测试数据构建器以匹配实际实体结构
3. **测试场景**: 添加边界条件和错误场景测试
4. **性能测试**: 集成 JMeter 或其他性能测试工具

这个测试套件为迁移后的 Spring Boot 应用提供了坚实的测试基础，确保代码质量和功能正确性。
