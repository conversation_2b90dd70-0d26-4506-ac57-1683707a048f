{"testGenerationConfig": {"description": "Spring Boot 应用测试生成配置", "version": "1.0.0", "enabled": true, "testTypes": {"unitTests": {"enabled": true, "description": "生成单元测试", "includes": ["controllers", "services", "repositories"]}, "integrationTests": {"enabled": true, "description": "生成集成测试", "includes": ["apiWorkflows", "databaseIntegration", "componentInteraction"]}, "testData": {"enabled": true, "description": "生成测试数据构建器和固定装置", "includes": ["builders", "fixtures", "factories"]}}, "testFrameworks": {"junit": {"version": "5.10.1", "enabled": true, "annotations": ["@Test", "@BeforeEach", "@AfterEach", "@ParameterizedTest"]}, "mockito": {"version": "5.8.0", "enabled": true, "annotations": ["@Mock", "@MockBean", "@InjectMocks", "@ExtendWith(MockitoExtension.class)"]}, "springBootTest": {"enabled": true, "annotations": ["@SpringBootTest", "@WebMvcTest", "@DataJpaTest", "@TestConfiguration"]}, "assertj": {"version": "3.25.1", "enabled": true, "description": "流畅的断言库"}}, "testPatterns": {"naming": {"testClassSuffix": "Test", "integrationTestSuffix": "IntegrationTest", "testDataBuilderSuffix": "TestDataBuilder", "testFixtureSuffix": "TestFixture"}, "packages": {"testBasePackage": "{basePackage}", "controllerTests": "{basePackage}.controller", "serviceTests": "{basePackage}.service", "repositoryTests": "{basePackage}.repository", "integrationTests": "{basePackage}.integration", "testData": "{basePackage}.testdata", "testConfig": "{basePackage}.config"}, "methods": {"testMethodPrefix": "test", "setupMethodName": "setUp", "teardownMethodName": "tearDown"}}, "testScenarios": {"controllerTests": {"crudOperations": {"enabled": true, "operations": [{"name": "getAllEntities", "httpMethod": "GET", "path": "/{entityPath}", "expectedStatus": "OK"}, {"name": "getEntityById", "httpMethod": "GET", "path": "/{entityPath}/{id}", "expectedStatus": "OK"}, {"name": "createEntity", "httpMethod": "POST", "path": "/{entityPath}", "expectedStatus": "CREATED"}, {"name": "updateEntity", "httpMethod": "PUT", "path": "/{entityPath}/{id}", "expectedStatus": "OK"}, {"name": "deleteEntity", "httpMethod": "DELETE", "path": "/{entityPath}/{id}", "expectedStatus": "NO_CONTENT"}]}, "errorHandling": {"enabled": true, "scenarios": ["entityNotFound", "invalidInput", "validationErrors"]}}, "serviceTests": {"businessLogic": {"enabled": true, "scenarios": ["findAll", "findById", "save", "update", "delete"]}, "exceptionHandling": {"enabled": true, "exceptions": ["EntityNotFoundException", "ValidationException", "BusinessLogicException"]}}, "repositoryTests": {"dataAccess": {"enabled": true, "scenarios": ["saveAndFindById", "findAll", "delete", "existsById", "count"]}, "customQueries": {"enabled": true, "description": "测试自定义查询方法"}}, "integrationTests": {"apiWorkflows": {"enabled": true, "workflows": ["crudWorkflow", "businessWorkflow", "errorWorkflow"]}, "healthChecks": {"enabled": true, "endpoints": ["/actuator/health", "/actuator/info"]}}}, "testData": {"defaultValues": {"Post": {"title": "Test Post Title", "content": "Test post content", "createdDate": "LocalDateTime.now()"}, "User": {"username": "testuser", "email": "<EMAIL>", "firstName": "Test", "lastName": "User"}}, "builders": {"fluentInterface": true, "staticFactoryMethods": true, "defaultValueMethods": true}, "fixtures": {"multipleEntities": true, "validEntities": true, "invalidEntities": false}}, "testEnvironment": {"database": {"type": "H2", "mode": "memory", "url": "jdbc:h2:mem:testdb", "ddlAuto": "create-drop"}, "profiles": {"active": "test", "additional": []}, "properties": {"logging": {"level": "WARN", "sqlLogging": false}, "jpa": {"showSql": false, "formatSql": false}}}, "codeGeneration": {"formatting": {"indentation": "    ", "lineLength": 120, "importOrganization": true}, "comments": {"enabled": true, "includeGeneratedBy": true, "includeTimestamp": false}, "annotations": {"suppressWarnings": true, "generatedAnnotation": false}}, "validation": {"compileGenerated": true, "runGeneratedTests": false, "validateTestCoverage": false, "minimumCoverageThreshold": 80}, "output": {"generateReadme": true, "generateTestReport": true, "generateRunInstructions": true}}}