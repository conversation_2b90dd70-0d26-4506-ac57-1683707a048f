package com.phodal.legacy;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.io.TempDir;
import picocli.CommandLine;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.PrintStream;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for the main CLI application.
 * Verifies that the CLI commands work correctly and produce expected output.
 */
class CliAppTest {
    
    private final ByteArrayOutputStream outContent = new ByteArrayOutputStream();
    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private final PrintStream originalOut = System.out;
    private final PrintStream originalErr = System.err;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUpStreams() {
        System.setOut(new PrintStream(outContent));
        System.setErr(new PrintStream(errContent));
    }
    
    @AfterEach
    void restoreStreams() {
        System.setOut(originalOut);
        System.setErr(originalErr);
    }
    
    @Test
    void testMainCommandWithoutArguments() {
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute();
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("JSP to Spring Boot Migration Tool"));
        assertTrue(output.contains("Use --help to see available commands"));
    }
    
    @Test
    void testHelpCommand() {
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("--help");
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Convert legacy JSP projects to Spring Boot applications"));
        assertTrue(output.contains("analyze"));
        assertTrue(output.contains("convert"));
        assertTrue(output.contains("validate"));
    }
    
    @Test
    void testVersionCommand() {
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("--version");
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("jsp2springboot 1.0.0"));
    }
    
    @Test
    void testAnalyzeCommandWithValidDirectory() {
        // Create a temporary directory structure
        File projectDir = tempDir.resolve("test-project").toFile();
        projectDir.mkdirs();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("analyze", projectDir.getAbsolutePath());
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Starting analysis of legacy JSP project"));
        assertTrue(output.contains("Analysis completed successfully"));
    }
    
    @Test
    void testAnalyzeCommandWithInvalidDirectory() {
        File nonExistentDir = new File("/non/existent/directory");
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("analyze", nonExistentDir.getAbsolutePath());
        
        assertEquals(1, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Project path does not exist"));
    }
    
    @Test
    void testConvertCommandWithValidDirectories() {
        // Create temporary directories
        File sourceDir = tempDir.resolve("source-project").toFile();
        File targetDir = tempDir.resolve("target-project").toFile();
        sourceDir.mkdirs();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("convert", 
                                 sourceDir.getAbsolutePath(), 
                                 targetDir.getAbsolutePath());
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Starting conversion from JSP to Spring Boot"));
        assertTrue(output.contains("Conversion completed successfully"));
    }
    
    @Test
    void testConvertCommandWithInvalidSource() {
        File nonExistentSource = new File("/non/existent/source");
        File targetDir = tempDir.resolve("target-project").toFile();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("convert", 
                                 nonExistentSource.getAbsolutePath(), 
                                 targetDir.getAbsolutePath());
        
        assertEquals(1, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Source path does not exist"));
    }
    
    @Test
    void testValidateCommandWithValidDirectory() {
        // Create a temporary directory
        File projectDir = tempDir.resolve("spring-boot-project").toFile();
        projectDir.mkdirs();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("validate", projectDir.getAbsolutePath());
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Starting validation of Spring Boot project"));
        assertTrue(output.contains("Validation completed successfully"));
    }
    
    @Test
    void testValidateCommandWithInvalidDirectory() {
        File nonExistentDir = new File("/non/existent/directory");
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("validate", nonExistentDir.getAbsolutePath());
        
        assertEquals(1, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Project path does not exist"));
    }
    
    @Test
    void testAnalyzeCommandWithOptions() {
        File projectDir = tempDir.resolve("test-project").toFile();
        projectDir.mkdirs();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("analyze", 
                                 projectDir.getAbsolutePath(),
                                 "--include-jsp=false",
                                 "--include-java=true");
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Include JSP files: false"));
        assertTrue(output.contains("Include Java sources: true"));
    }
    
    @Test
    void testConvertCommandWithCustomOptions() {
        File sourceDir = tempDir.resolve("source-project").toFile();
        File targetDir = tempDir.resolve("target-project").toFile();
        sourceDir.mkdirs();
        
        CliApp app = new CliApp();
        CommandLine cmd = new CommandLine(app);
        
        int exitCode = cmd.execute("convert", 
                                 sourceDir.getAbsolutePath(), 
                                 targetDir.getAbsolutePath(),
                                 "--spring-boot-version=3.2.1",
                                 "--java-version=17",
                                 "--package-name=com.test.migrated");
        
        assertEquals(0, exitCode);
        String output = outContent.toString();
        assertTrue(output.contains("Spring Boot version: 3.2.1"));
        assertTrue(output.contains("Java version: 17"));
        assertTrue(output.contains("Base package: com.test.migrated"));
    }
}
