package com.phodal.legacy.integration;

import com.phodal.legacy.mapper.JspBytecodeMapper;
import com.phodal.legacy.model.graph.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.parser.BytecodeAnalyzer;
import com.phodal.legacy.parser.JspAnalyzer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for JSP to bytecode mapping functionality.
 * Tests the complete flow from JSP analysis to mapping creation.
 */
class JspBytecodeMappingIntegrationTest {
    
    @TempDir
    Path tempDir;
    
    private JspAnalyzer jspAnalyzer;
    private BytecodeAnalyzer bytecodeAnalyzer;
    private JspBytecodeMapper mapper;
    
    @BeforeEach
    void setUp() {
        jspAnalyzer = new JspAnalyzer();
        bytecodeAnalyzer = new BytecodeAnalyzer();
        mapper = new JspBytecodeMapper();
    }
    
    @Test
    void testJspAnalysisWithMappingHints() throws IOException {
        // Create a test JSP file
        Path jspFile = tempDir.resolve("test.jsp");
        String jspContent = """
            <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
            <%@ page import="java.util.List, com.example.UserService" %>
            <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test JSP</title>
            </head>
            <body>
                <%! 
                    private String formatName(String name) {
                        return name.toUpperCase();
                    }
                    
                    private List<String> users = new ArrayList<>();
                %>
                
                <% 
                    String userName = request.getParameter("name");
                    if (userName != null) {
                        users.add(userName);
                    }
                %>
                
                <h1>Welcome <%= formatName(userName) %></h1>
                
                <c:forEach var="user" items="${users}">
                    <p>${user}</p>
                </c:forEach>
            </body>
            </html>
            """;
        
        Files.writeString(jspFile, jspContent);
        
        // Analyze JSP with mapping hints
        ComponentModel jspComponent = jspAnalyzer.analyzeJspFileWithMappingHints(jspFile, tempDir);
        
        assertNotNull(jspComponent);
        assertEquals(ComponentModel.ComponentType.JSP_PAGE, jspComponent.getType());
        assertEquals("test.jsp", jspComponent.getName());
        
        // Verify mapping hints were extracted
        Object mappingHints = jspComponent.getProperty("mappingHints");
        assertNotNull(mappingHints);
        assertTrue(mappingHints instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> hints = (Map<String, Object>) mappingHints;
        
        // Check method signatures
        assertTrue(hints.containsKey("methodSignatures"));
        
        // Check variables
        assertTrue(hints.containsKey("variables"));
        
        // Verify dependencies were extracted
        assertTrue(jspComponent.getDependencies().contains("java.util.List"));
        assertTrue(jspComponent.getDependencies().contains("com.example.UserService"));
        
        // Verify tag libraries were extracted
        Object tagLibs = jspComponent.getProperty("tagLibraries");
        assertNotNull(tagLibs);
        assertTrue(tagLibs instanceof List);
        
        @SuppressWarnings("unchecked")
        List<String> tagLibList = (List<String>) tagLibs;
        assertTrue(tagLibList.contains("http://java.sun.com/jsp/jstl/core"));
    }
    
    @Test
    void testEndToEndMappingFlow() {
        // Create mock JSP components
        ComponentModel jsp1 = createMockJspComponent("jsp1", "index.jsp", "/webapp/index.jsp");
        ComponentModel jsp2 = createMockJspComponent("jsp2", "login.jsp", "/webapp/login.jsp");
        
        // Create mock servlet components
        ComponentModel servlet1 = createMockServletComponent("servlet1", "index_jsp", "org.apache.jsp.index_jsp");
        ComponentModel servlet2 = createMockServletComponent("servlet2", "login_jsp", "org.apache.jsp.login_jsp");
        
        // Create mock non-servlet component
        ComponentModel javaClass = createMockJavaComponent("java1", "UserService", "com.example.UserService");
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp1, jsp2);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet1, servlet2, javaClass);
        
        // Create mappings
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        // Verify mappings were created
        assertNotNull(registry);
        assertEquals(2, registry.getAllMappings().size());
        
        // Verify specific mappings
        JspBytecodeMapping mapping1 = registry.getMapping("jsp1");
        assertNotNull(mapping1);
        assertEquals("/webapp/index.jsp", mapping1.getJspPath());
        assertEquals("org.apache.jsp.index_jsp", mapping1.getCompiledServletClass());
        
        JspBytecodeMapping mapping2 = registry.getMapping("jsp2");
        assertNotNull(mapping2);
        assertEquals("/webapp/login.jsp", mapping2.getJspPath());
        assertEquals("org.apache.jsp.login_jsp", mapping2.getCompiledServletClass());
        
        // Verify reverse mappings work
        assertEquals(1, registry.getMappingsByBytecodeClass("org.apache.jsp.index_jsp").size());
        assertEquals(1, registry.getMappingsByBytecodeClass("org.apache.jsp.login_jsp").size());
        
        // Verify statistics
        MappingRegistry.MappingStatistics stats = registry.getStatistics();
        assertEquals(2, stats.totalMappings);
        assertEquals(2, stats.totalBytecodeClasses);
        assertTrue(stats.totalClassMappings >= 2);
        assertTrue(stats.totalDependencyMappings >= 0);
    }
    
    @Test
    void testMappingWithComplexDependencies() {
        // Create JSP with complex dependencies
        ComponentModel jsp = createMockJspComponent("jsp1", "complex.jsp", "/webapp/complex.jsp");
        jsp.addDependency("java.util.List");
        jsp.addDependency("java.util.Map");
        jsp.addDependency("com.example.UserService");
        jsp.addDependency("com.example.OrderService");
        
        // Create servlet with overlapping dependencies
        ComponentModel servlet = createMockServletComponent("servlet1", "complex_jsp", "org.apache.jsp.complex_jsp");
        servlet.addDependency("javax.servlet.http.HttpServlet");
        servlet.addDependency("java.util.List"); // Common
        servlet.addDependency("java.util.Map"); // Common
        servlet.addDependency("com.example.UserService"); // Common
        servlet.addDependency("com.example.ProductService"); // Different
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        JspBytecodeMapping mapping = registry.getMapping("jsp1");
        assertNotNull(mapping);
        
        // Verify dependency mappings
        Map<String, String> depMappings = mapping.getDependencyMappings();
        assertTrue(depMappings.containsKey("java.util.List"));
        assertTrue(depMappings.containsKey("java.util.Map"));
        assertTrue(depMappings.containsKey("com.example.UserService"));
        
        // Verify confidence is high due to many common dependencies
        Object confidence = mapping.getMetadata().get("mappingConfidence");
        assertNotNull(confidence);
        assertTrue((Double) confidence > 0.7);
    }
    
    @Test
    void testMappingWithTagLibraries() {
        ComponentModel jsp = createMockJspComponent("jsp1", "tags.jsp", "/webapp/tags.jsp");
        jsp.getProperties().put("tagLibraries", Arrays.asList(
            "http://java.sun.com/jsp/jstl/core",
            "http://java.sun.com/jsp/jstl/fmt",
            "http://www.springframework.org/tags"
        ));
        
        ComponentModel servlet = createMockServletComponent("servlet1", "tags_jsp", "org.apache.jsp.tags_jsp");
        
        List<ComponentModel> jspComponents = Arrays.asList(jsp);
        List<ComponentModel> bytecodeComponents = Arrays.asList(servlet);
        
        MappingRegistry registry = mapper.createMappings(jspComponents, bytecodeComponents);
        
        JspBytecodeMapping mapping = registry.getMapping("jsp1");
        assertNotNull(mapping);
        
        // Verify tag library mappings
        assertEquals(3, mapping.getTagLibraryMappings().size());
        assertTrue(mapping.getTagLibraryMappings().contains("http://java.sun.com/jsp/jstl/core"));
        assertTrue(mapping.getTagLibraryMappings().contains("http://java.sun.com/jsp/jstl/fmt"));
        assertTrue(mapping.getTagLibraryMappings().contains("http://www.springframework.org/tags"));
    }
    
    private ComponentModel createMockJspComponent(String id, String name, String sourcePath) {
        ComponentModel component = new ComponentModel(id, name, ComponentModel.ComponentType.JSP_PAGE);
        component.setSourcePath(sourcePath);
        component.addProperty("language", "java");
        component.addProperty("contentType", "text/html");
        component.addProperty("complexity", 5);
        return component;
    }
    
    private ComponentModel createMockServletComponent(String id, String name, String className) {
        ComponentModel component = new ComponentModel(id, name, ComponentModel.ComponentType.SERVLET);
        component.addProperty("className", className);
        component.addProperty("methods", Arrays.asList("_jspService", "doGet", "doPost"));
        component.addProperty("complexity", 8);
        component.addDependency("javax.servlet.http.HttpServlet");
        return component;
    }
    
    private ComponentModel createMockJavaComponent(String id, String name, String className) {
        ComponentModel component = new ComponentModel(id, name, ComponentModel.ComponentType.JAVA_CLASS);
        component.addProperty("className", className);
        component.addProperty("methods", Arrays.asList("getUser", "saveUser", "deleteUser"));
        return component;
    }
}
