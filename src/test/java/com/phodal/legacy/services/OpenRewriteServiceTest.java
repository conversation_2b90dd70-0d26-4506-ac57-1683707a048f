package com.phodal.legacy.services;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * Unit tests for OpenRewriteService
 */
class OpenRewriteServiceTest {

    private OpenRewriteService openRewriteService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        openRewriteService = new OpenRewriteService();
    }

    @Test
    void testUpgradeJavaVersion_ValidVersions() throws IOException {
        // Create a test Java file
        Path javaFile = tempDir.resolve("src/main/java/Test.java");
        Files.createDirectories(javaFile.getParent());
        Files.write(javaFile, "public class Test { }".getBytes());

        // Test should not throw exception - but may warn about missing recipes
        try {
            openRewriteService.upgradeJavaVersion(tempDir, "8", "11");
        } catch (Exception e) {
            // Expected - recipes may not be available in test environment
            assertTrue(e.getMessage().contains("upgrade") || e.getMessage().contains("recipe"));
        }
    }

    @Test
    void testUpgradeJavaVersion_InvalidVersions() {
        // Test with invalid version format - should throw NumberFormatException or IOException
        assertThrows(Exception.class, () -> {
            openRewriteService.upgradeJavaVersion(tempDir, "invalid", "11");
        });
    }

    @Test
    void testUpgradeSpringBoot_ValidVersion() throws IOException {
        // Create a test pom.xml file
        Path pomFile = tempDir.resolve("pom.xml");
        String pomContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0">
                <modelVersion>4.0.0</modelVersion>
                <groupId>com.example</groupId>
                <artifactId>test</artifactId>
                <version>1.0.0</version>
            </project>
            """;
        Files.write(pomFile, pomContent.getBytes());

        // Test should handle gracefully - recipes may not be available
        try {
            openRewriteService.upgradeSpringBoot(tempDir, "3.2.1");
        } catch (Exception e) {
            // Expected - recipes may not be available in test environment
            assertTrue(e.getMessage().contains("Spring Boot") || e.getMessage().contains("recipe"));
        }
    }

    @Test
    void testModernizeCode_EmptyProject() throws IOException {
        // Test with empty project directory - should handle gracefully
        try {
            openRewriteService.modernizeCode(tempDir);
        } catch (Exception e) {
            // Expected - may fail due to missing recipes or empty project
            assertNotNull(e.getMessage());
        }
    }

    @Test
    void testModernizeCode_WithJavaFiles() throws IOException {
        // Create test Java files
        Path javaFile1 = tempDir.resolve("src/main/java/Example.java");
        Files.createDirectories(javaFile1.getParent());
        Files.write(javaFile1, """
            import java.util.ArrayList;
            import java.util.List;

            public class Example {
                public List<String> getList() {
                    return new ArrayList<String>();
                }
            }
            """.getBytes());

        Path javaFile2 = tempDir.resolve("src/main/java/Another.java");
        Files.write(javaFile2, """
            public class Another {
                public boolean isValid(String input) {
                    if (input != null && input.length() > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            """.getBytes());

        // Test should handle gracefully
        try {
            openRewriteService.modernizeCode(tempDir);
        } catch (Exception e) {
            // Expected - recipes may not be available
            assertNotNull(e.getMessage());
        }
    }

    @Test
    void testApplyComprehensiveModernization() throws IOException {
        // Create a basic project structure
        Path srcDir = tempDir.resolve("src/main/java");
        Files.createDirectories(srcDir);

        Path javaFile = srcDir.resolve("TestClass.java");
        Files.write(javaFile, """
            import java.util.ArrayList;
            import java.util.List;

            public class TestClass {
                private List<String> items = new ArrayList<String>();

                public boolean isEmpty() {
                    if (items.size() == 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            """.getBytes());

        // Test should handle gracefully - comprehensive modernization may fail due to missing dependencies
        try {
            openRewriteService.applyComprehensiveModernization(tempDir, "17", "3.2.1");
        } catch (Exception e) {
            // Expected - may fail due to missing rewrite.yml or recipe dependencies
            assertTrue(e.getMessage().contains("modernization") ||
                      e.getMessage().contains("recipe") ||
                      e.getMessage().contains("failed"));
        }
    }

    @Test
    void testUpgradeTarget_Enum() {
        // Test enum values
        assertEquals("java-11", OpenRewriteService.UpgradeTarget.JAVA_11.getName());
        assertEquals("11", OpenRewriteService.UpgradeTarget.JAVA_11.getVersion());
        
        assertEquals("java-17", OpenRewriteService.UpgradeTarget.JAVA_17.getName());
        assertEquals("17", OpenRewriteService.UpgradeTarget.JAVA_17.getVersion());
        
        assertEquals("java-21", OpenRewriteService.UpgradeTarget.JAVA_21.getName());
        assertEquals("21", OpenRewriteService.UpgradeTarget.JAVA_21.getVersion());
        
        assertEquals("spring-boot-3", OpenRewriteService.UpgradeTarget.SPRING_BOOT_3.getName());
        assertEquals("3.2.1", OpenRewriteService.UpgradeTarget.SPRING_BOOT_3.getVersion());
    }

    @Test
    void testUpgradeJavaVersion_NonExistentProject() {
        Path nonExistentPath = tempDir.resolve("non-existent");
        
        // Should handle non-existent project gracefully
        assertDoesNotThrow(() -> {
            openRewriteService.upgradeJavaVersion(nonExistentPath, "8", "11");
        });
    }

    @Test
    void testUpgradeSpringBoot_UnsupportedVersion() throws IOException {
        // Test with unsupported Spring Boot version
        assertDoesNotThrow(() -> {
            openRewriteService.upgradeSpringBoot(tempDir, "2.7.0");
        });
    }
}
