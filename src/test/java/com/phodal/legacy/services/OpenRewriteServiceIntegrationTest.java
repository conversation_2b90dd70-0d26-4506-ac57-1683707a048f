package com.phodal.legacy.services;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for OpenRewriteService
 * Tests the actual OpenRewrite functionality with real recipes
 */
@SpringBootTest
@TestPropertySource(properties = {
    "logging.level.com.phodal.legacy=DEBUG"
})
class OpenRewriteServiceIntegrationTest {

    private OpenRewriteService openRewriteService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        openRewriteService = new OpenRewriteService();
    }

    @Test
    void testJavaModernization_DiamondOperator() throws IOException {
        // Create a Java file that can benefit from diamond operator
        Path javaFile = tempDir.resolve("src/main/java/DiamondTest.java");
        Files.createDirectories(javaFile.getParent());
        
        String originalCode = """
            import java.util.ArrayList;
            import java.util.HashMap;
            import java.util.List;
            import java.util.Map;
            
            public class DiamondTest {
                private List<String> items = new ArrayList<String>();
                private Map<String, Integer> counts = new HashMap<String, Integer>();
                
                public void addItem(String item) {
                    items.add(item);
                    counts.put(item, counts.getOrDefault(item, 0) + 1);
                }
            }
            """;
        Files.write(javaFile, originalCode.getBytes());

        // Apply modernization
        openRewriteService.modernizeCode(tempDir);

        // Verify the file still exists and is readable
        assertTrue(Files.exists(javaFile));
        String modifiedCode = Files.readString(javaFile);
        assertNotNull(modifiedCode);
        assertTrue(modifiedCode.contains("DiamondTest"));
    }

    @Test
    void testJavaModernization_BooleanSimplification() throws IOException {
        // Create a Java file with boolean expressions that can be simplified
        Path javaFile = tempDir.resolve("src/main/java/BooleanTest.java");
        Files.createDirectories(javaFile.getParent());
        
        String originalCode = """
            public class BooleanTest {
                public boolean isValid(String input) {
                    if (input != null && input.length() > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
                
                public boolean isEmpty(String input) {
                    if (input == null || input.length() == 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            """;
        Files.write(javaFile, originalCode.getBytes());

        // Apply modernization
        openRewriteService.modernizeCode(tempDir);

        // Verify the file still exists and is readable
        assertTrue(Files.exists(javaFile));
        String modifiedCode = Files.readString(javaFile);
        assertNotNull(modifiedCode);
        assertTrue(modifiedCode.contains("BooleanTest"));
    }

    @Test
    void testMavenProjectModernization() throws IOException {
        // Create a Maven project structure
        Path pomFile = tempDir.resolve("pom.xml");
        String pomContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>test-project</artifactId>
                <version>1.0.0</version>
                <packaging>jar</packaging>
                
                <properties>
                    <maven.compiler.source>8</maven.compiler.source>
                    <maven.compiler.target>8</maven.compiler.target>
                </properties>
                
                <dependencies>
                    <dependency>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                        <version>4.12</version>
                        <scope>test</scope>
                    </dependency>
                </dependencies>
            </project>
            """;
        Files.write(pomFile, pomContent.getBytes());

        // Create a Java file
        Path javaFile = tempDir.resolve("src/main/java/Example.java");
        Files.createDirectories(javaFile.getParent());
        Files.write(javaFile, """
            import java.util.ArrayList;
            import java.util.List;
            
            public class Example {
                public List<String> createList() {
                    return new ArrayList<String>();
                }
            }
            """.getBytes());

        // Apply Java version upgrade
        openRewriteService.upgradeJavaVersion(tempDir, "8", "11");

        // Verify files still exist
        assertTrue(Files.exists(pomFile));
        assertTrue(Files.exists(javaFile));
        
        // Verify content is still valid
        String pomContentAfter = Files.readString(pomFile);
        assertNotNull(pomContentAfter);
        assertTrue(pomContentAfter.contains("test-project"));
    }

    @Test
    void testGradleProjectModernization() throws IOException {
        // Create a Gradle project structure
        Path buildFile = tempDir.resolve("build.gradle");
        String buildContent = """
            plugins {
                id 'java'
            }
            
            group = 'com.example'
            version = '1.0.0'
            
            java {
                sourceCompatibility = '8'
                targetCompatibility = '8'
            }
            
            dependencies {
                testImplementation 'junit:junit:4.12'
            }
            """;
        Files.write(buildFile, buildContent.getBytes());

        // Create a Java file
        Path javaFile = tempDir.resolve("src/main/java/GradleExample.java");
        Files.createDirectories(javaFile.getParent());
        Files.write(javaFile, """
            import java.util.HashMap;
            import java.util.Map;
            
            public class GradleExample {
                public Map<String, String> createMap() {
                    return new HashMap<String, String>();
                }
            }
            """.getBytes());

        // Apply modernization
        openRewriteService.modernizeCode(tempDir);

        // Verify files still exist
        assertTrue(Files.exists(buildFile));
        assertTrue(Files.exists(javaFile));
        
        // Verify content is still valid
        String buildContentAfter = Files.readString(buildFile);
        assertNotNull(buildContentAfter);
        assertTrue(buildContentAfter.contains("com.example"));
    }

    @Test
    void testComprehensiveModernization_WithCustomRecipe() throws IOException {
        // Create project structure
        Path srcDir = tempDir.resolve("src/main/java");
        Files.createDirectories(srcDir);
        
        // Create a Java file with multiple modernization opportunities
        Path javaFile = srcDir.resolve("ComprehensiveTest.java");
        Files.write(javaFile, """
            import java.util.ArrayList;
            import java.util.HashMap;
            import java.util.List;
            import java.util.Map;
            
            public class ComprehensiveTest {
                private List<String> items = new ArrayList<String>();
                private Map<String, Integer> counts = new HashMap<String, Integer>();
                
                public boolean hasItems() {
                    if (items.size() > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
                
                public void processItems() {
                    for (int i = 0; i < items.size(); i++) {
                        String item = items.get(i);
                        if (item != null && item.length() > 0) {
                            counts.put(item, counts.getOrDefault(item, 0) + 1);
                        }
                    }
                }
            }
            """.getBytes());

        // Create a simple rewrite.yml for testing
        Path rewriteConfig = tempDir.resolve("rewrite.yml");
        Files.write(rewriteConfig, """
            ---
            type: specs.openrewrite.org/v1beta/recipe
            name: com.phodal.legacy.modernize.JavaModernization
            displayName: Test Comprehensive Modernization
            description: Test comprehensive modernization recipe
            recipeList:
              - org.openrewrite.java.cleanup.UseDiamondOperator
              - org.openrewrite.java.cleanup.SimplifyBooleanReturn
              - org.openrewrite.java.format.AutoFormat
            """.getBytes());

        // Apply comprehensive modernization
        assertDoesNotThrow(() -> {
            openRewriteService.applyComprehensiveModernization(tempDir, "17", "3.2.1");
        });

        // Verify the file still exists and is readable
        assertTrue(Files.exists(javaFile));
        String modifiedCode = Files.readString(javaFile);
        assertNotNull(modifiedCode);
        assertTrue(modifiedCode.contains("ComprehensiveTest"));
    }

    @Test
    void testSpringBootUpgrade_WithValidProject() throws IOException {
        // Create a Spring Boot project structure
        Path pomFile = tempDir.resolve("pom.xml");
        String pomContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>1.0.0</version>
                <packaging>jar</packaging>
                
                <parent>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-parent</artifactId>
                    <version>2.7.0</version>
                    <relativePath/>
                </parent>
                
                <dependencies>
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </dependency>
                </dependencies>
            </project>
            """;
        Files.write(pomFile, pomContent.getBytes());

        // Apply Spring Boot upgrade
        assertDoesNotThrow(() -> {
            openRewriteService.upgradeSpringBoot(tempDir, "3.2.1");
        });

        // Verify the file still exists
        assertTrue(Files.exists(pomFile));
        String pomContentAfter = Files.readString(pomFile);
        assertNotNull(pomContentAfter);
        assertTrue(pomContentAfter.contains("spring-boot-test"));
    }
}
