package com.phodal.legacy.parser;

import com.phodal.legacy.model.graph.ComponentModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for WebXmlParser to verify web.xml parsing and analysis capabilities.
 */
class WebXmlParserTest {
    
    private WebXmlParser webXmlParser;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        webXmlParser = new WebXmlParser();
    }
    
    @Test
    void testAnalyzeSimpleWebXml() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app xmlns="http://java.sun.com/xml/ns/javaee"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
                     http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
                     version="3.0">
                
                <display-name>Test Web Application</display-name>
                <description>A simple test web application</description>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        assertEquals("web.xml", component.getName());
        assertEquals(ComponentModel.ComponentType.WEB_XML, component.getType());
        assertEquals(webXmlFile.toString(), component.getSourcePath());
        
        // Check metadata
        Map<String, Object> properties = component.getProperties();
        assertEquals("3.0", properties.get("version"));
        assertEquals("Test Web Application", properties.get("displayName"));
        assertEquals("A simple test web application", properties.get("description"));
    }
    
    @Test
    void testAnalyzeWebXmlWithServlets() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <servlet>
                    <servlet-name>TestServlet</servlet-name>
                    <servlet-class>com.example.TestServlet</servlet-class>
                    <init-param>
                        <param-name>debug</param-name>
                        <param-value>true</param-value>
                    </init-param>
                    <init-param>
                        <param-name>maxConnections</param-name>
                        <param-value>100</param-value>
                    </init-param>
                </servlet>
                
                <servlet>
                    <servlet-name>JspServlet</servlet-name>
                    <jsp-file>/WEB-INF/jsp/test.jsp</jsp-file>
                </servlet>
                
                <servlet-mapping>
                    <servlet-name>TestServlet</servlet-name>
                    <url-pattern>/test/*</url-pattern>
                </servlet-mapping>
                
                <servlet-mapping>
                    <servlet-name>JspServlet</servlet-name>
                    <url-pattern>/jsp-test</url-pattern>
                </servlet-mapping>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check dependencies (servlet classes)
        assertTrue(component.getDependencies().contains("com.example.TestServlet"));
        
        // Check servlets in metadata
        @SuppressWarnings("unchecked")
        List<WebXmlParser.ServletInfo> servlets = (List<WebXmlParser.ServletInfo>) component.getProperties().get("servlets");
        assertEquals(2, servlets.size());
        
        WebXmlParser.ServletInfo testServlet = servlets.stream()
            .filter(s -> "TestServlet".equals(s.getName()))
            .findFirst()
            .orElse(null);
        assertNotNull(testServlet);
        assertEquals("com.example.TestServlet", testServlet.getClassName());
        assertEquals("true", testServlet.getInitParams().get("debug"));
        assertEquals("100", testServlet.getInitParams().get("maxConnections"));
        
        WebXmlParser.ServletInfo jspServlet = servlets.stream()
            .filter(s -> "JspServlet".equals(s.getName()))
            .findFirst()
            .orElse(null);
        assertNotNull(jspServlet);
        assertEquals("/WEB-INF/jsp/test.jsp", jspServlet.getJspFile());
        
        // Check servlet mappings
        @SuppressWarnings("unchecked")
        Map<String, String> servletMappings = (Map<String, String>) component.getProperties().get("servletMappings");
        assertEquals("/test/*", servletMappings.get("TestServlet"));
        assertEquals("/jsp-test", servletMappings.get("JspServlet"));
    }
    
    @Test
    void testAnalyzeWebXmlWithFilters() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <filter>
                    <filter-name>EncodingFilter</filter-name>
                    <filter-class>com.example.EncodingFilter</filter-class>
                    <init-param>
                        <param-name>encoding</param-name>
                        <param-value>UTF-8</param-value>
                    </init-param>
                </filter>
                
                <filter>
                    <filter-name>LoggingFilter</filter-name>
                    <filter-class>com.example.LoggingFilter</filter-class>
                </filter>
                
                <filter-mapping>
                    <filter-name>EncodingFilter</filter-name>
                    <url-pattern>/*</url-pattern>
                </filter-mapping>
                
                <filter-mapping>
                    <filter-name>LoggingFilter</filter-name>
                    <servlet-name>TestServlet</servlet-name>
                </filter-mapping>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check dependencies (filter classes)
        assertTrue(component.getDependencies().contains("com.example.EncodingFilter"));
        assertTrue(component.getDependencies().contains("com.example.LoggingFilter"));
        
        // Check filters in metadata
        @SuppressWarnings("unchecked")
        List<WebXmlParser.FilterInfo> filters = (List<WebXmlParser.FilterInfo>) component.getProperties().get("filters");
        assertEquals(2, filters.size());
        
        WebXmlParser.FilterInfo encodingFilter = filters.stream()
            .filter(f -> "EncodingFilter".equals(f.getName()))
            .findFirst()
            .orElse(null);
        assertNotNull(encodingFilter);
        assertEquals("com.example.EncodingFilter", encodingFilter.getClassName());
        assertEquals("UTF-8", encodingFilter.getInitParams().get("encoding"));
        
        // Check filter mappings
        @SuppressWarnings("unchecked")
        Map<String, String> filterMappings = (Map<String, String>) component.getProperties().get("filterMappings");
        assertEquals("/*", filterMappings.get("EncodingFilter"));
        assertEquals("TestServlet", filterMappings.get("LoggingFilter"));
    }
    
    @Test
    void testAnalyzeWebXmlWithListeners() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <listener>
                    <listener-class>com.example.AppContextListener</listener-class>
                </listener>
                
                <listener>
                    <listener-class>com.example.SessionListener</listener-class>
                </listener>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check dependencies (listener classes)
        assertTrue(component.getDependencies().contains("com.example.AppContextListener"));
        assertTrue(component.getDependencies().contains("com.example.SessionListener"));
        
        // Check listeners in metadata
        @SuppressWarnings("unchecked")
        List<String> listeners = (List<String>) component.getProperties().get("listeners");
        assertEquals(2, listeners.size());
        assertTrue(listeners.contains("com.example.AppContextListener"));
        assertTrue(listeners.contains("com.example.SessionListener"));
    }
    
    @Test
    void testAnalyzeWebXmlWithContextParams() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <context-param>
                    <param-name>contextConfigLocation</param-name>
                    <param-value>/WEB-INF/applicationContext.xml</param-value>
                </context-param>
                
                <context-param>
                    <param-name>spring.profiles.active</param-name>
                    <param-value>production</param-value>
                </context-param>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check context parameters
        @SuppressWarnings("unchecked")
        Map<String, String> contextParams = (Map<String, String>) component.getProperties().get("contextParams");
        assertEquals("/WEB-INF/applicationContext.xml", contextParams.get("contextConfigLocation"));
        assertEquals("production", contextParams.get("spring.profiles.active"));
    }
    
    @Test
    void testAnalyzeWebXmlWithWelcomeFiles() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <welcome-file-list>
                    <welcome-file>index.jsp</welcome-file>
                    <welcome-file>index.html</welcome-file>
                    <welcome-file>default.jsp</welcome-file>
                </welcome-file-list>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check welcome files
        @SuppressWarnings("unchecked")
        List<String> welcomeFiles = (List<String>) component.getProperties().get("welcomeFiles");
        assertEquals(3, welcomeFiles.size());
        assertTrue(welcomeFiles.contains("index.jsp"));
        assertTrue(welcomeFiles.contains("index.html"));
        assertTrue(welcomeFiles.contains("default.jsp"));
    }
    
    @Test
    void testAnalyzeWebXmlWithErrorPages() throws Exception {
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                
                <error-page>
                    <error-code>404</error-code>
                    <location>/error/404.jsp</location>
                </error-page>
                
                <error-page>
                    <exception-type>java.lang.Exception</exception-type>
                    <location>/error/general.jsp</location>
                </error-page>
                
            </web-app>
            """;
        
        Path webXmlFile = tempDir.resolve("web.xml");
        Files.writeString(webXmlFile, webXmlContent);
        
        ComponentModel component = webXmlParser.analyzeWebXmlFile(webXmlFile, tempDir);
        
        assertNotNull(component);
        
        // Check error pages
        @SuppressWarnings("unchecked")
        List<WebXmlParser.ErrorPageInfo> errorPages = (List<WebXmlParser.ErrorPageInfo>) component.getProperties().get("errorPages");
        assertEquals(2, errorPages.size());
        
        WebXmlParser.ErrorPageInfo errorPage404 = errorPages.stream()
            .filter(ep -> "404".equals(ep.getErrorCode()))
            .findFirst()
            .orElse(null);
        assertNotNull(errorPage404);
        assertEquals("/error/404.jsp", errorPage404.getLocation());
        
        WebXmlParser.ErrorPageInfo exceptionPage = errorPages.stream()
            .filter(ep -> "java.lang.Exception".equals(ep.getExceptionType()))
            .findFirst()
            .orElse(null);
        assertNotNull(exceptionPage);
        assertEquals("/error/general.jsp", exceptionPage.getLocation());
    }
    
    @Test
    void testAnalyzeMultipleWebXmlFiles() throws Exception {
        // Create multiple web.xml files in different directories
        Path webInfDir = tempDir.resolve("WEB-INF");
        Path subDir = tempDir.resolve("sub/WEB-INF");
        Files.createDirectories(webInfDir);
        Files.createDirectories(subDir);
        
        String webXml1Content = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                <display-name>Main App</display-name>
            </web-app>
            """;
        
        String webXml2Content = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="2.5">
                <display-name>Sub App</display-name>
            </web-app>
            """;
        
        Files.writeString(webInfDir.resolve("web.xml"), webXml1Content);
        Files.writeString(subDir.resolve("web.xml"), webXml2Content);
        
        List<ComponentModel> components = webXmlParser.analyzeWebXmlFiles(tempDir);
        
        assertEquals(2, components.size());
        
        // Verify both files were analyzed
        boolean foundMainApp = false, foundSubApp = false;
        for (ComponentModel component : components) {
            String displayName = (String) component.getProperties().get("displayName");
            if ("Main App".equals(displayName)) {
                foundMainApp = true;
                assertEquals("3.0", component.getProperties().get("version"));
            } else if ("Sub App".equals(displayName)) {
                foundSubApp = true;
                assertEquals("2.5", component.getProperties().get("version"));
            }
        }
        
        assertTrue(foundMainApp);
        assertTrue(foundSubApp);
    }
    
    @Test
    void testAnalyzeEmptyDirectory() throws Exception {
        List<ComponentModel> components = webXmlParser.analyzeWebXmlFiles(tempDir);
        
        assertTrue(components.isEmpty());
    }
}
