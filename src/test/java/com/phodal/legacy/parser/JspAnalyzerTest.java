package com.phodal.legacy.parser;

import com.phodal.legacy.model.graph.ComponentModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for JspAnalyzer to verify JSP file parsing and analysis capabilities.
 */
class JspAnalyzerTest {
    
    private JspAnalyzer jspAnalyzer;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        jspAnalyzer = new JspAnalyzer();
    }
    
    @Test
    void testAnalyzeSimpleJspFile() throws IOException {
        // Create a simple JSP file
        String jspContent = """
            <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test JSP</title>
            </head>
            <body>
                <h1>Hello World</h1>
                <% String message = "Welcome to JSP"; %>
                <p><%= message %></p>
            </body>
            </html>
            """;
        
        Path jspFile = tempDir.resolve("test.jsp");
        Files.writeString(jspFile, jspContent);
        
        ComponentModel component = jspAnalyzer.analyzeJspFile(jspFile, tempDir);
        
        assertNotNull(component);
        assertEquals("test.jsp", component.getName());
        assertEquals(ComponentModel.ComponentType.JSP_PAGE, component.getType());
        assertEquals(jspFile.toString(), component.getSourcePath());
        
        // Check metadata
        Map<String, Object> properties = component.getProperties();
        assertEquals("java", properties.get("language"));
        assertEquals("text/html; charset=UTF-8", properties.get("contentType"));
        assertEquals("UTF-8", properties.get("pageEncoding"));
        assertTrue((Boolean) properties.get("sessionEnabled"));
        
        // Check complexity (should have scriptlet and expression)
        Integer complexity = (Integer) properties.get("complexity");
        assertNotNull(complexity);
        assertTrue(complexity > 0);
    }
    
    @Test
    void testAnalyzeJspWithImports() throws IOException {
        String jspContent = """
            <%@ page language="java" contentType="text/html; charset=UTF-8" 
                     import="java.util.List,java.util.ArrayList,com.example.User" %>
            <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
            <%@ include file="header.jsp" %>
            
            <html>
            <body>
                <% List<String> items = new ArrayList<>(); %>
                <c:forEach var="item" items="${items}">
                    <p>${item}</p>
                </c:forEach>
            </body>
            </html>
            """;
        
        Path jspFile = tempDir.resolve("complex.jsp");
        Files.writeString(jspFile, jspContent);
        
        ComponentModel component = jspAnalyzer.analyzeJspFile(jspFile, tempDir);
        
        assertNotNull(component);
        
        // Check imports
        @SuppressWarnings("unchecked")
        List<String> imports = (List<String>) component.getProperties().get("imports");
        assertNotNull(imports);
        assertTrue(imports.contains("java.util.List"));
        assertTrue(imports.contains("java.util.ArrayList"));
        assertTrue(imports.contains("com.example.User"));
        
        // Check includes
        @SuppressWarnings("unchecked")
        List<String> includes = (List<String>) component.getProperties().get("includes");
        assertNotNull(includes);
        assertTrue(includes.contains("header.jsp"));
        
        // Check tag libraries
        @SuppressWarnings("unchecked")
        List<String> tagLibs = (List<String>) component.getProperties().get("tagLibraries");
        assertNotNull(tagLibs);
        assertTrue(tagLibs.contains("http://java.sun.com/jsp/jstl/core"));
        
        // Check dependencies
        assertTrue(component.getDependencies().contains("java.util.List"));
        assertTrue(component.getDependencies().contains("java.util.ArrayList"));
        assertTrue(component.getDependencies().contains("com.example.User"));
    }
    
    @Test
    void testAnalyzeJspWithDeclarations() throws IOException {
        String jspContent = """
            <%@ page language="java" %>
            <%!
                private String formatMessage(String msg) {
                    return "Formatted: " + msg;
                }
                
                private static final String CONSTANT = "TEST";
            %>
            
            <html>
            <body>
                <% String message = "Hello"; %>
                <%= formatMessage(message) %>
                <%= CONSTANT %>
            </body>
            </html>
            """;
        
        Path jspFile = tempDir.resolve("declarations.jsp");
        Files.writeString(jspFile, jspContent);
        
        ComponentModel component = jspAnalyzer.analyzeJspFile(jspFile, tempDir);
        
        assertNotNull(component);
        
        // Check complexity (declarations add more complexity)
        Integer complexity = (Integer) component.getProperties().get("complexity");
        assertNotNull(complexity);
        assertTrue(complexity >= 6); // 2 declarations (3 each) + scriptlet (2) + expression (1)
    }
    
    @Test
    void testAnalyzeMultipleJspFiles() throws IOException {
        // Create multiple JSP files
        String jsp1Content = """
            <%@ page language="java" %>
            <html><body><h1>Page 1</h1></body></html>
            """;
        
        String jsp2Content = """
            <%@ page language="java" import="java.util.Date" %>
            <html><body><%= new Date() %></body></html>
            """;
        
        Path jsp1 = tempDir.resolve("page1.jsp");
        Path jsp2 = tempDir.resolve("page2.jsp");
        Files.writeString(jsp1, jsp1Content);
        Files.writeString(jsp2, jsp2Content);
        
        List<ComponentModel> components = jspAnalyzer.analyzeJspFiles(tempDir);
        
        assertEquals(2, components.size());
        
        // Verify both files were analyzed
        boolean foundPage1 = false, foundPage2 = false;
        for (ComponentModel component : components) {
            if ("page1.jsp".equals(component.getName())) {
                foundPage1 = true;
                assertEquals(ComponentModel.ComponentType.JSP_PAGE, component.getType());
            } else if ("page2.jsp".equals(component.getName())) {
                foundPage2 = true;
                assertEquals(ComponentModel.ComponentType.JSP_PAGE, component.getType());
                assertTrue(component.getDependencies().contains("java.util.Date"));
            }
        }
        
        assertTrue(foundPage1);
        assertTrue(foundPage2);
    }
    
    @Test
    void testAnalyzeJspWithErrorPage() throws IOException {
        String jspContent = """
            <%@ page language="java" errorPage="error.jsp" %>
            <html><body><h1>Main Page</h1></body></html>
            """;
        
        Path jspFile = tempDir.resolve("main.jsp");
        Files.writeString(jspFile, jspContent);
        
        ComponentModel component = jspAnalyzer.analyzeJspFile(jspFile, tempDir);
        
        assertNotNull(component);
        assertEquals("error.jsp", component.getProperties().get("errorPage"));
    }
    
    @Test
    void testAnalyzeJspWithSessionDisabled() throws IOException {
        String jspContent = """
            <%@ page language="java" session="false" %>
            <html><body><h1>No Session Page</h1></body></html>
            """;
        
        Path jspFile = tempDir.resolve("nosession.jsp");
        Files.writeString(jspFile, jspContent);
        
        ComponentModel component = jspAnalyzer.analyzeJspFile(jspFile, tempDir);
        
        assertNotNull(component);
        assertFalse((Boolean) component.getProperties().get("sessionEnabled"));
    }
    
    @Test
    void testAnalyzeEmptyDirectory() throws IOException {
        List<ComponentModel> components = jspAnalyzer.analyzeJspFiles(tempDir);
        
        assertTrue(components.isEmpty());
    }
    
    @Test
    void testAnalyzeNonJspFiles() throws IOException {
        // Create non-JSP files
        Path htmlFile = tempDir.resolve("test.html");
        Path txtFile = tempDir.resolve("test.txt");
        Files.writeString(htmlFile, "<html><body>HTML</body></html>");
        Files.writeString(txtFile, "Plain text");
        
        List<ComponentModel> components = jspAnalyzer.analyzeJspFiles(tempDir);
        
        assertTrue(components.isEmpty());
    }
    
    @Test
    void testAnalyzeJspxFile() throws IOException {
        String jspxContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0">
                <jsp:directive.page contentType="text/html"/>
                <html>
                    <body>
                        <h1>JSPX Page</h1>
                    </body>
                </html>
            </jsp:root>
            """;
        
        Path jspxFile = tempDir.resolve("test.jspx");
        Files.writeString(jspxFile, jspxContent);
        
        List<ComponentModel> components = jspAnalyzer.analyzeJspFiles(tempDir);
        
        assertEquals(1, components.size());
        ComponentModel component = components.get(0);
        assertEquals("test.jspx", component.getName());
        assertEquals(ComponentModel.ComponentType.JSP_PAGE, component.getType());
    }
}
