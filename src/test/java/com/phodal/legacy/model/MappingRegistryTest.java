package com.phodal.legacy.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for MappingRegistry to verify mapping storage and retrieval functionality.
 */
class MappingRegistryTest {
    
    private MappingRegistry registry;
    
    @BeforeEach
    void setUp() {
        registry = new MappingRegistry();
    }
    
    @Test
    void testRegisterAndRetrieveMapping() {
        JspBytecodeMapping mapping = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping.setCompiledServletClass("org.apache.jsp.index_jsp");
        mapping.addClassMapping("index.jsp", "org.apache.jsp.index_jsp");
        mapping.addDependencyMapping("java.util.List", "java.util.List");
        
        registry.registerMapping(mapping);
        
        // Test retrieval by ID
        JspBytecodeMapping retrieved = registry.getMapping("jsp1");
        assertNotNull(retrieved);
        assertEquals("jsp1", retrieved.getJspComponentId());
        assertEquals("/webapp/index.jsp", retrieved.getJspPath());
        assertEquals("org.apache.jsp.index_jsp", retrieved.getCompiledServletClass());
        
        // Test retrieval by path
        JspBytecodeMapping retrievedByPath = registry.getMappingByPath("/webapp/index.jsp");
        assertNotNull(retrievedByPath);
        assertEquals(mapping, retrievedByPath);
    }
    
    @Test
    void testRegisterMultipleMappings() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.setCompiledServletClass("org.apache.jsp.index_jsp");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        mapping2.setCompiledServletClass("org.apache.jsp.login_jsp");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        assertEquals(2, registry.getAllMappings().size());
        assertNotNull(registry.getMapping("jsp1"));
        assertNotNull(registry.getMapping("jsp2"));
    }
    
    @Test
    void testGetMappingsByBytecodeClass() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.setCompiledServletClass("org.apache.jsp.index_jsp");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/admin/index.jsp");
        mapping2.setCompiledServletClass("org.apache.jsp.index_jsp"); // Same servlet class
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        Set<JspBytecodeMapping> mappings = registry.getMappingsByBytecodeClass("org.apache.jsp.index_jsp");
        assertEquals(2, mappings.size());
        assertTrue(mappings.contains(mapping1));
        assertTrue(mappings.contains(mapping2));
        
        // Test with non-existent class
        Set<JspBytecodeMapping> emptyMappings = registry.getMappingsByBytecodeClass("nonexistent");
        assertTrue(emptyMappings.isEmpty());
    }
    
    @Test
    void testGetMappingsByDependency() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.addDependencyMapping("java.util.List", "java.util.List");
        mapping1.addDependencyMapping("com.example.Service", "com.example.ServiceImpl");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        mapping2.addDependencyMapping("java.util.List", "java.util.List");
        mapping2.addDependencyMapping("com.example.UserService", "com.example.UserServiceImpl");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        // Test common dependency
        Set<JspBytecodeMapping> mappings = registry.getMappingsByDependency("java.util.List");
        assertEquals(2, mappings.size());
        
        // Test specific dependency
        Set<JspBytecodeMapping> specificMappings = registry.getMappingsByDependency("com.example.ServiceImpl");
        assertEquals(1, specificMappings.size());
        assertTrue(specificMappings.contains(mapping1));
    }
    
    @Test
    void testRemoveMapping() {
        JspBytecodeMapping mapping = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping.setCompiledServletClass("org.apache.jsp.index_jsp");
        mapping.addDependencyMapping("java.util.List", "java.util.List");
        
        registry.registerMapping(mapping);
        assertTrue(registry.hasMapping("jsp1"));
        
        boolean removed = registry.removeMapping("jsp1");
        assertTrue(removed);
        assertFalse(registry.hasMapping("jsp1"));
        assertNull(registry.getMapping("jsp1"));
        
        // Test removing non-existent mapping
        boolean notRemoved = registry.removeMapping("nonexistent");
        assertFalse(notRemoved);
    }
    
    @Test
    void testClearMappings() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        assertEquals(2, registry.getAllMappings().size());
        
        registry.clear();
        
        assertEquals(0, registry.getAllMappings().size());
        assertFalse(registry.hasMapping("jsp1"));
        assertFalse(registry.hasMapping("jsp2"));
    }
    
    @Test
    void testMappingStatistics() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.setCompiledServletClass("org.apache.jsp.index_jsp");
        mapping1.addClassMapping("index.jsp", "org.apache.jsp.index_jsp");
        mapping1.addClassMapping("BaseClass", "org.apache.jsp.BaseClass");
        mapping1.addMethodMapping("jspService", "_jspService", 1, "servlet_method");
        mapping1.addMethodMapping("init", "init", 5, "servlet_method");
        mapping1.addDependencyMapping("java.util.List", "java.util.List");
        mapping1.addDependencyMapping("com.example.Service", "com.example.ServiceImpl");
        mapping1.addTagLibraryMapping("http://java.sun.com/jsp/jstl/core");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        mapping2.setCompiledServletClass("org.apache.jsp.login_jsp");
        mapping2.addClassMapping("login.jsp", "org.apache.jsp.login_jsp");
        mapping2.addMethodMapping("jspService", "_jspService", 1, "servlet_method");
        mapping2.addDependencyMapping("java.util.Map", "java.util.Map");
        mapping2.addTagLibraryMapping("http://java.sun.com/jsp/jstl/fmt");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        MappingRegistry.MappingStatistics stats = registry.getStatistics();
        
        assertEquals(2, stats.totalMappings);
        assertEquals(2, stats.totalBytecodeClasses);
        assertEquals(3, stats.totalDependencies); // java.util.List, com.example.ServiceImpl, java.util.Map
        assertEquals(3, stats.totalClassMappings); // 2 from mapping1, 1 from mapping2
        assertEquals(3, stats.totalMethodMappings); // 2 from mapping1, 1 from mapping2
        assertEquals(3, stats.totalDependencyMappings); // 2 from mapping1, 1 from mapping2
        assertEquals(2, stats.totalTagLibraryMappings); // 1 from each mapping
    }
    
    @Test
    void testIsBytecodeClassMapped() {
        JspBytecodeMapping mapping = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping.setCompiledServletClass("org.apache.jsp.index_jsp");
        
        registry.registerMapping(mapping);
        
        assertTrue(registry.isBytecodeClassMapped("org.apache.jsp.index_jsp"));
        assertFalse(registry.isBytecodeClassMapped("com.example.NonExistent"));
    }
    
    @Test
    void testGetMappedBytecodeClasses() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.setCompiledServletClass("org.apache.jsp.index_jsp");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        mapping2.setCompiledServletClass("org.apache.jsp.login_jsp");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        Set<String> mappedClasses = registry.getMappedBytecodeClasses();
        assertEquals(2, mappedClasses.size());
        assertTrue(mappedClasses.contains("org.apache.jsp.index_jsp"));
        assertTrue(mappedClasses.contains("org.apache.jsp.login_jsp"));
    }
    
    @Test
    void testGetMappedDependencies() {
        JspBytecodeMapping mapping1 = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping1.addDependencyMapping("java.util.List", "java.util.List");
        mapping1.addDependencyMapping("com.example.Service", "com.example.ServiceImpl");
        
        JspBytecodeMapping mapping2 = new JspBytecodeMapping("jsp2", "/webapp/login.jsp");
        mapping2.addDependencyMapping("java.util.Map", "java.util.Map");
        
        registry.registerMapping(mapping1);
        registry.registerMapping(mapping2);
        
        Set<String> mappedDependencies = registry.getMappedDependencies();
        assertEquals(3, mappedDependencies.size());
        assertTrue(mappedDependencies.contains("java.util.List"));
        assertTrue(mappedDependencies.contains("com.example.ServiceImpl"));
        assertTrue(mappedDependencies.contains("java.util.Map"));
    }
    
    @Test
    void testToString() {
        JspBytecodeMapping mapping = new JspBytecodeMapping("jsp1", "/webapp/index.jsp");
        mapping.setCompiledServletClass("org.apache.jsp.index_jsp");
        
        registry.registerMapping(mapping);
        
        String registryString = registry.toString();
        assertNotNull(registryString);
        assertTrue(registryString.contains("mappings=1"));
        assertTrue(registryString.contains("classes=1"));
    }
}
