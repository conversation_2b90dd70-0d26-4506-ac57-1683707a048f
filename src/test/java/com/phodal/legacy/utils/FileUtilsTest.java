package com.phodal.legacy.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for FileUtils utility methods.
 */
class FileUtilsTest {
    
    @TempDir
    Path tempDir;
    
    private Path testFile;
    private Path testDir;
    
    @BeforeEach
    void setUp() throws IOException {
        testFile = tempDir.resolve("test.txt");
        testDir = tempDir.resolve("testdir");
        Files.createDirectories(testDir);
    }
    
    @Test
    void testReadAndWriteFileContent() throws IOException {
        String content = "Hello, World!\nThis is a test file.";
        
        // Write content
        FileUtils.writeFileContent(testFile, content);
        assertTrue(Files.exists(testFile));
        
        // Read content
        String readContent = FileUtils.readFileContent(testFile);
        assertEquals(content, readContent);
    }
    
    @Test
    void testWriteFileContentCreatesDirectories() throws IOException {
        Path nestedFile = tempDir.resolve("nested/deep/file.txt");
        String content = "Nested file content";
        
        FileUtils.writeFileContent(nestedFile, content);
        
        assertTrue(Files.exists(nestedFile));
        assertEquals(content, FileUtils.readFileContent(nestedFile));
    }
    
    @Test
    void testCopyFile() throws IOException {
        String content = "File to copy";
        FileUtils.writeFileContent(testFile, content);
        
        Path destination = tempDir.resolve("copied.txt");
        FileUtils.copyFile(testFile, destination);
        
        assertTrue(Files.exists(destination));
        assertEquals(content, FileUtils.readFileContent(destination));
    }
    
    @Test
    void testFindFilesByExtensions() throws IOException {
        // Create test files
        Files.createFile(testDir.resolve("test1.jsp"));
        Files.createFile(testDir.resolve("test2.java"));
        Files.createFile(testDir.resolve("test3.jsp"));
        Files.createFile(testDir.resolve("test4.txt"));
        
        Set<String> jspExtensions = Set.of(".jsp");
        List<Path> jspFiles = FileUtils.findFilesByExtensions(testDir, jspExtensions);
        
        assertEquals(2, jspFiles.size());
        assertTrue(jspFiles.stream().allMatch(path -> path.toString().endsWith(".jsp")));
    }
    
    @Test
    void testFindJspFiles() throws IOException {
        // Create test files
        Files.createFile(testDir.resolve("page1.jsp"));
        Files.createFile(testDir.resolve("page2.jspx"));
        Files.createFile(testDir.resolve("tag1.tag"));
        Files.createFile(testDir.resolve("test.java"));
        
        List<Path> jspFiles = FileUtils.findJspFiles(testDir);
        
        assertEquals(3, jspFiles.size());
        assertTrue(jspFiles.stream().anyMatch(path -> path.getFileName().toString().equals("page1.jsp")));
        assertTrue(jspFiles.stream().anyMatch(path -> path.getFileName().toString().equals("page2.jspx")));
        assertTrue(jspFiles.stream().anyMatch(path -> path.getFileName().toString().equals("tag1.tag")));
    }
    
    @Test
    void testFindJavaFiles() throws IOException {
        // Create test files
        Files.createFile(testDir.resolve("Test1.java"));
        Files.createFile(testDir.resolve("Test2.java"));
        Files.createFile(testDir.resolve("test.jsp"));
        
        List<Path> javaFiles = FileUtils.findJavaFiles(testDir);
        
        assertEquals(2, javaFiles.size());
        assertTrue(javaFiles.stream().allMatch(path -> path.toString().endsWith(".java")));
    }
    
    @Test
    void testFindWebXmlFiles() throws IOException {
        // Create test files
        Path webInfDir = testDir.resolve("WEB-INF");
        Files.createDirectories(webInfDir);
        Files.createFile(webInfDir.resolve("web.xml"));
        Files.createFile(testDir.resolve("other.xml"));
        
        List<Path> webXmlFiles = FileUtils.findWebXmlFiles(testDir);
        
        assertEquals(1, webXmlFiles.size());
        assertTrue(webXmlFiles.get(0).getFileName().toString().equals("web.xml"));
    }
    
    @Test
    void testGetFileExtension() {
        Path jspFile = Path.of("test.jsp");
        Path javaFile = Path.of("Test.java");
        Path noExtFile = Path.of("README");
        
        assertEquals(".jsp", FileUtils.getFileExtension(jspFile));
        assertEquals(".java", FileUtils.getFileExtension(javaFile));
        assertEquals("", FileUtils.getFileExtension(noExtFile));
    }
    
    @Test
    void testGetFileNameWithoutExtension() {
        Path jspFile = Path.of("test.jsp");
        Path javaFile = Path.of("Test.java");
        Path noExtFile = Path.of("README");
        
        assertEquals("test", FileUtils.getFileNameWithoutExtension(jspFile));
        assertEquals("Test", FileUtils.getFileNameWithoutExtension(javaFile));
        assertEquals("README", FileUtils.getFileNameWithoutExtension(noExtFile));
    }
    
    @Test
    void testIsJspFile() {
        assertTrue(FileUtils.isJspFile(Path.of("test.jsp")));
        assertTrue(FileUtils.isJspFile(Path.of("test.jspx")));
        assertTrue(FileUtils.isJspFile(Path.of("test.tag")));
        assertFalse(FileUtils.isJspFile(Path.of("test.java")));
        assertFalse(FileUtils.isJspFile(Path.of("test.txt")));
    }
    
    @Test
    void testIsJavaFile() {
        assertTrue(FileUtils.isJavaFile(Path.of("Test.java")));
        assertFalse(FileUtils.isJavaFile(Path.of("test.jsp")));
        assertFalse(FileUtils.isJavaFile(Path.of("test.txt")));
    }
    
    @Test
    void testGetRelativePath() {
        Path basePath = Path.of("/project/src");
        Path targetPath = Path.of("/project/src/main/java/Test.java");
        
        String relativePath = FileUtils.getRelativePath(basePath, targetPath);
        assertEquals("main/java/Test.java", relativePath);
    }
    
    @Test
    void testCountFilesByExtension() throws IOException {
        // Create test files
        Files.createFile(testDir.resolve("test1.jsp"));
        Files.createFile(testDir.resolve("test2.jsp"));
        Files.createFile(testDir.resolve("test1.java"));
        Files.createFile(testDir.resolve("test.txt"));
        Files.createFile(testDir.resolve("README"));
        
        Map<String, Integer> counts = FileUtils.countFilesByExtension(testDir);
        
        assertEquals(2, counts.get(".jsp"));
        assertEquals(1, counts.get(".java"));
        assertEquals(1, counts.get(".txt"));
        assertEquals(1, counts.get("no-extension"));
    }
    
    @Test
    void testBackupFile() throws IOException {
        String content = "Original content";
        FileUtils.writeFileContent(testFile, content);
        
        Path backupPath = FileUtils.backupFile(testFile);
        
        assertTrue(Files.exists(backupPath));
        assertEquals(content, FileUtils.readFileContent(backupPath));
        assertTrue(backupPath.getFileName().toString().startsWith("test.txt.backup."));
    }
    
    @Test
    void testEnsureDirectoryExists() throws IOException {
        Path newDir = tempDir.resolve("new/nested/directory");
        assertFalse(Files.exists(newDir));
        
        FileUtils.ensureDirectoryExists(newDir);
        
        assertTrue(Files.exists(newDir));
        assertTrue(Files.isDirectory(newDir));
    }
}
