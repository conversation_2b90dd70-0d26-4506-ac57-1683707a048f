package com.phodal.legacy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Application configuration for the JSP to Spring Boot migration tool.
 * Contains settings for migration behavior, output preferences, and tool configurations.
 */
@Configuration
@ConfigurationProperties(prefix = "jsp2springboot")
public class AppConfig {
    
    private Migration migration = new Migration();
    private Output output = new Output();
    private Analysis analysis = new Analysis();
    private Generation generation = new Generation();
    private Validation validation = new Validation();
    
    // Getters and setters
    public Migration getMigration() { return migration; }
    public void setMigration(Migration migration) { this.migration = migration; }
    
    public Output getOutput() { return output; }
    public void setOutput(Output output) { this.output = output; }
    
    public Analysis getAnalysis() { return analysis; }
    public void setAnalysis(Analysis analysis) { this.analysis = analysis; }
    
    public Generation getGeneration() { return generation; }
    public void setGeneration(Generation generation) { this.generation = generation; }
    
    public Validation getValidation() { return validation; }
    public void setValidation(Validation validation) { this.validation = validation; }
    
    /**
     * Migration-specific configuration
     */
    public static class Migration {
        private String springBootVersion = "3.2.1";
        private String javaVersion = "17";
        private String basePackage = "com.example.migrated";
        private boolean preserveOriginalStructure = false;
        private boolean generateTests = true;
        private boolean enableAiAssistance = true;
        
        // Getters and setters
        public String getSpringBootVersion() { return springBootVersion; }
        public void setSpringBootVersion(String springBootVersion) { this.springBootVersion = springBootVersion; }
        
        public String getJavaVersion() { return javaVersion; }
        public void setJavaVersion(String javaVersion) { this.javaVersion = javaVersion; }
        
        public String getBasePackage() { return basePackage; }
        public void setBasePackage(String basePackage) { this.basePackage = basePackage; }
        
        public boolean isPreserveOriginalStructure() { return preserveOriginalStructure; }
        public void setPreserveOriginalStructure(boolean preserveOriginalStructure) { this.preserveOriginalStructure = preserveOriginalStructure; }
        
        public boolean isGenerateTests() { return generateTests; }
        public void setGenerateTests(boolean generateTests) { this.generateTests = generateTests; }
        
        public boolean isEnableAiAssistance() { return enableAiAssistance; }
        public void setEnableAiAssistance(boolean enableAiAssistance) { this.enableAiAssistance = enableAiAssistance; }
    }
    
    /**
     * Output configuration
     */
    public static class Output {
        private String format = "detailed"; // detailed, summary, json
        private boolean colorOutput = true;
        private boolean progressBar = true;
        private String logLevel = "INFO";
        private boolean saveReports = true;
        private String reportDirectory = "./migration-reports";
        
        // Getters and setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public boolean isColorOutput() { return colorOutput; }
        public void setColorOutput(boolean colorOutput) { this.colorOutput = colorOutput; }
        
        public boolean isProgressBar() { return progressBar; }
        public void setProgressBar(boolean progressBar) { this.progressBar = progressBar; }
        
        public String getLogLevel() { return logLevel; }
        public void setLogLevel(String logLevel) { this.logLevel = logLevel; }
        
        public boolean isSaveReports() { return saveReports; }
        public void setSaveReports(boolean saveReports) { this.saveReports = saveReports; }
        
        public String getReportDirectory() { return reportDirectory; }
        public void setReportDirectory(String reportDirectory) { this.reportDirectory = reportDirectory; }
    }
    
    /**
     * Analysis configuration
     */
    public static class Analysis {
        private boolean analyzeJsp = true;
        private boolean analyzeJava = true;
        private boolean analyzeWebXml = true;
        private boolean analyzeDependencies = true;
        private boolean deepAnalysis = false;
        private int maxDepth = 10;
        private Map<String, String> customPatterns = new HashMap<>();
        
        // Getters and setters
        public boolean isAnalyzeJsp() { return analyzeJsp; }
        public void setAnalyzeJsp(boolean analyzeJsp) { this.analyzeJsp = analyzeJsp; }
        
        public boolean isAnalyzeJava() { return analyzeJava; }
        public void setAnalyzeJava(boolean analyzeJava) { this.analyzeJava = analyzeJava; }
        
        public boolean isAnalyzeWebXml() { return analyzeWebXml; }
        public void setAnalyzeWebXml(boolean analyzeWebXml) { this.analyzeWebXml = analyzeWebXml; }
        
        public boolean isAnalyzeDependencies() { return analyzeDependencies; }
        public void setAnalyzeDependencies(boolean analyzeDependencies) { this.analyzeDependencies = analyzeDependencies; }
        
        public boolean isDeepAnalysis() { return deepAnalysis; }
        public void setDeepAnalysis(boolean deepAnalysis) { this.deepAnalysis = deepAnalysis; }
        
        public int getMaxDepth() { return maxDepth; }
        public void setMaxDepth(int maxDepth) { this.maxDepth = maxDepth; }
        
        public Map<String, String> getCustomPatterns() { return customPatterns; }
        public void setCustomPatterns(Map<String, String> customPatterns) { this.customPatterns = customPatterns; }
    }
    
    /**
     * Code generation configuration
     */
    public static class Generation {
        private String templateEngine = "javapoet"; // javapoet, freemarker
        private boolean generateComments = true;
        private boolean generateJavadoc = true;
        private String codeStyle = "google"; // google, oracle, custom
        private boolean optimizeImports = true;
        private Map<String, String> templateOverrides = new HashMap<>();
        
        // Getters and setters
        public String getTemplateEngine() { return templateEngine; }
        public void setTemplateEngine(String templateEngine) { this.templateEngine = templateEngine; }
        
        public boolean isGenerateComments() { return generateComments; }
        public void setGenerateComments(boolean generateComments) { this.generateComments = generateComments; }
        
        public boolean isGenerateJavadoc() { return generateJavadoc; }
        public void setGenerateJavadoc(boolean generateJavadoc) { this.generateJavadoc = generateJavadoc; }
        
        public String getCodeStyle() { return codeStyle; }
        public void setCodeStyle(String codeStyle) { this.codeStyle = codeStyle; }
        
        public boolean isOptimizeImports() { return optimizeImports; }
        public void setOptimizeImports(boolean optimizeImports) { this.optimizeImports = optimizeImports; }
        
        public Map<String, String> getTemplateOverrides() { return templateOverrides; }
        public void setTemplateOverrides(Map<String, String> templateOverrides) { this.templateOverrides = templateOverrides; }
    }
    
    /**
     * Validation configuration
     */
    public static class Validation {
        private boolean validateSyntax = true;
        private boolean validateCompilation = true;
        private boolean runTests = true;
        private boolean checkDependencies = true;
        private int timeoutSeconds = 300;
        
        // Getters and setters
        public boolean isValidateSyntax() { return validateSyntax; }
        public void setValidateSyntax(boolean validateSyntax) { this.validateSyntax = validateSyntax; }
        
        public boolean isValidateCompilation() { return validateCompilation; }
        public void setValidateCompilation(boolean validateCompilation) { this.validateCompilation = validateCompilation; }
        
        public boolean isRunTests() { return runTests; }
        public void setRunTests(boolean runTests) { this.runTests = runTests; }
        
        public boolean isCheckDependencies() { return checkDependencies; }
        public void setCheckDependencies(boolean checkDependencies) { this.checkDependencies = checkDependencies; }
        
        public int getTimeoutSeconds() { return timeoutSeconds; }
        public void setTimeoutSeconds(int timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }
    }
}
