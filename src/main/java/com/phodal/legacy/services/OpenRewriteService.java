package com.phodal.legacy.services;

import com.phodal.legacy.utils.LoggingUtils;
import org.openrewrite.*;
import org.openrewrite.config.Environment;
import org.openrewrite.config.YamlResourceLoader;
import org.openrewrite.java.JavaParser;
import org.openrewrite.maven.MavenParser;
import org.openrewrite.gradle.GradleParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for applying OpenRewrite recipes to modernize Java code
 */
public class OpenRewriteService {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenRewriteService.class);
    
    public enum UpgradeTarget {
        JAVA_11("java-11", "11"),
        JAVA_17("java-17", "17"), 
        JAVA_21("java-21", "21"),
        SPRING_BOOT_3("spring-boot-3", "3.2.1");
        
        private final String name;
        private final String version;
        
        UpgradeTarget(String name, String version) {
            this.name = name;
            this.version = version;
        }
        
        public String getName() { return name; }
        public String getVersion() { return version; }
    }
    
    /**
     * Apply JDK upgrade recipes to the project
     */
    public void upgradeJavaVersion(Path projectPath, String fromVersion, String toVersion) throws IOException {
        LoggingUtils.logOperationStart("upgradeJavaVersion", "OpenRewriteService");
        
        try {
            logger.info("Upgrading Java version from {} to {} in project: {}", fromVersion, toVersion, projectPath);
            
            List<Recipe> recipes = getJavaUpgradeRecipes(fromVersion, toVersion);
            applyRecipes(projectPath, recipes);
            
            logger.info("Java version upgrade completed successfully");
            LoggingUtils.logOperationComplete("upgradeJavaVersion", "OpenRewriteService");
            
        } catch (Exception e) {
            logger.error("Failed to upgrade Java version: {}", e.getMessage());
            LoggingUtils.logOperationError("upgradeJavaVersion", "OpenRewriteService", e);
            throw new IOException("Java version upgrade failed", e);
        }
    }
    
    /**
     * Apply Spring Boot upgrade recipes
     */
    public void upgradeSpringBoot(Path projectPath, String targetVersion) throws IOException {
        LoggingUtils.logOperationStart("upgradeSpringBoot", "OpenRewriteService");
        
        try {
            logger.info("Upgrading Spring Boot to version {} in project: {}", targetVersion, projectPath);
            
            List<Recipe> recipes = getSpringBootUpgradeRecipes(targetVersion);
            applyRecipes(projectPath, recipes);
            
            logger.info("Spring Boot upgrade completed successfully");
            LoggingUtils.logOperationComplete("upgradeSpringBoot", "OpenRewriteService");
            
        } catch (Exception e) {
            logger.error("Failed to upgrade Spring Boot: {}", e.getMessage());
            LoggingUtils.logOperationError("upgradeSpringBoot", "OpenRewriteService", e);
            throw new IOException("Spring Boot upgrade failed", e);
        }
    }
    
    /**
     * Apply code modernization recipes
     */
    public void modernizeCode(Path projectPath) throws IOException {
        LoggingUtils.logOperationStart("modernizeCode", "OpenRewriteService");
        
        try {
            logger.info("Applying code modernization to project: {}", projectPath);
            
            List<Recipe> recipes = getCodeModernizationRecipes();
            applyRecipes(projectPath, recipes);
            
            logger.info("Code modernization completed successfully");
            LoggingUtils.logOperationComplete("modernizeCode", "OpenRewriteService");
            
        } catch (Exception e) {
            logger.error("Failed to modernize code: {}", e.getMessage());
            LoggingUtils.logOperationError("modernizeCode", "OpenRewriteService", e);
            throw new IOException("Code modernization failed", e);
        }
    }
    
    /**
     * Apply comprehensive modernization including JDK, Spring Boot, and code improvements
     */
    public void applyComprehensiveModernization(Path projectPath, String targetJavaVersion, String targetSpringBootVersion) throws IOException {
        LoggingUtils.logOperationStart("applyComprehensiveModernization", "OpenRewriteService");
        
        try {
            logger.info("Applying comprehensive modernization to project: {}", projectPath);
            logger.info("Target Java version: {}, Target Spring Boot version: {}", targetJavaVersion, targetSpringBootVersion);
            
            // Load custom recipes from YAML
            Environment environment = loadCustomRecipes();
            
            // Get the comprehensive modernization recipe
            Recipe recipe = environment.activateRecipes("com.phodal.legacy.modernize.JavaModernization");
            
            // Apply the recipe
            applyRecipe(projectPath, recipe);
            
            logger.info("Comprehensive modernization completed successfully");
            LoggingUtils.logOperationComplete("applyComprehensiveModernization", "OpenRewriteService");
            
        } catch (Exception e) {
            logger.error("Failed to apply comprehensive modernization: {}", e.getMessage());
            LoggingUtils.logOperationError("applyComprehensiveModernization", "OpenRewriteService", e);
            throw new IOException("Comprehensive modernization failed", e);
        }
    }
    
    private List<Recipe> getJavaUpgradeRecipes(String fromVersion, String toVersion) {
        List<Recipe> recipes = new ArrayList<>();

        try {
            Environment environment = Environment.builder()
                .scanRuntimeClasspath()
                .build();

            // Determine upgrade path
            int from = Integer.parseInt(fromVersion);
            int to = Integer.parseInt(toVersion);

            if (from <= 8 && to >= 11) {
                Recipe recipe = environment.activateRecipes("org.openrewrite.java.migrate.Java8toJava11");
                if (recipe != null) recipes.add(recipe);
            }
            if (from <= 11 && to >= 17) {
                Recipe recipe = environment.activateRecipes("org.openrewrite.java.migrate.Java11toJava17");
                if (recipe != null) recipes.add(recipe);
            }
            if (from <= 17 && to >= 21) {
                Recipe recipe = environment.activateRecipes("org.openrewrite.java.migrate.Java17toJava21");
                if (recipe != null) recipes.add(recipe);
            }
        } catch (Exception e) {
            logger.warn("Failed to load Java upgrade recipes: {}", e.getMessage());
        }

        return recipes;
    }
    
    private List<Recipe> getSpringBootUpgradeRecipes(String targetVersion) {
        List<Recipe> recipes = new ArrayList<>();

        try {
            Environment environment = Environment.builder()
                .scanRuntimeClasspath()
                .build();

            if (targetVersion.startsWith("3.")) {
                Recipe recipe = environment.activateRecipes("org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_2");
                if (recipe != null) recipes.add(recipe);
            }
        } catch (Exception e) {
            logger.warn("Failed to load Spring Boot upgrade recipes: {}", e.getMessage());
        }

        return recipes;
    }
    
    private List<Recipe> getCodeModernizationRecipes() {
        try {
            Environment environment = Environment.builder()
                .scanRuntimeClasspath()
                .build();

            List<Recipe> recipes = new ArrayList<>();

            // Add available cleanup recipes
            String[] recipeNames = {
                "org.openrewrite.java.cleanup.CommonDeclarationSiteTypeVariances",
                "org.openrewrite.java.cleanup.CommonStaticAnalysis",
                "org.openrewrite.java.cleanup.UnnecessaryParentheses",
                "org.openrewrite.java.cleanup.RemoveUnusedImports",
                "org.openrewrite.java.cleanup.RemoveUnusedLocalVariables",
                "org.openrewrite.java.cleanup.SimplifyBooleanExpression",
                "org.openrewrite.java.cleanup.SimplifyBooleanReturn",
                "org.openrewrite.java.cleanup.UseCollectionInterfaces",
                "org.openrewrite.java.cleanup.UseDiamondOperator",
                "org.openrewrite.java.cleanup.UseStringReplace",
                "org.openrewrite.java.format.AutoFormat"
            };

            for (String recipeName : recipeNames) {
                try {
                    Recipe recipe = environment.activateRecipes(recipeName);
                    if (recipe != null) {
                        recipes.add(recipe);
                    }
                } catch (Exception e) {
                    logger.debug("Recipe {} not available: {}", recipeName, e.getMessage());
                }
            }

            return recipes;
        } catch (Exception e) {
            logger.warn("Failed to load modernization recipes: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    private void applyRecipes(Path projectPath, List<Recipe> recipes) throws IOException {
        for (Recipe recipe : recipes) {
            applyRecipe(projectPath, recipe);
        }
    }
    
    private void applyRecipe(Path projectPath, Recipe recipe) throws IOException {
        try {
            // Parse source files
            List<SourceFile> sourceFiles = parseSourceFiles(projectPath);

            if (sourceFiles.isEmpty()) {
                logger.warn("No source files found to process in: {}", projectPath);
                return;
            }

            logger.info("Processing {} source files with recipe: {}", sourceFiles.size(), recipe.getName());

            // Create execution context
            ExecutionContext executionContext = new InMemoryExecutionContext();

            // Apply recipe - use a simpler approach for now
            List<Result> results = new ArrayList<>();
            for (SourceFile sourceFile : sourceFiles) {
                SourceFile after = (SourceFile) recipe.getVisitor().visit(sourceFile, executionContext);
                if (after != null && !after.equals(sourceFile)) {
                    results.add(new Result(sourceFile, after));
                }
            }

            // Write back changes
            int changedFiles = 0;
            for (Result result : results) {
                if (result.diff().isEmpty()) {
                    continue;
                }

                Path filePath = projectPath.resolve(result.getBefore().getSourcePath());
                Files.write(filePath, result.getAfter().printAll().getBytes());
                changedFiles++;

                logger.debug("Updated file: {}", filePath);
            }

            logger.info("Recipe {} applied successfully. Changed {} files.", recipe.getName(), changedFiles);

        } catch (Exception e) {
            logger.error("Failed to apply recipe {}: {}", recipe.getName(), e.getMessage());
            throw new IOException("Recipe application failed", e);
        }
    }
    
    private List<SourceFile> parseSourceFiles(Path projectPath) throws IOException {
        List<SourceFile> sourceFiles = new ArrayList<>();
        ExecutionContext executionContext = new InMemoryExecutionContext();

        // Parse Java files
        List<Path> javaFiles = Files.walk(projectPath)
            .filter(path -> path.toString().endsWith(".java"))
            .filter(path -> !path.toString().contains("/test/"))
            .collect(Collectors.toList());

        if (!javaFiles.isEmpty()) {
            JavaParser javaParser = JavaParser.fromJavaVersion()
                .classpath(JavaParser.runtimeClasspath())
                .build();

            javaParser.parse(javaFiles, projectPath, executionContext)
                .forEach(sourceFiles::add);
        }

        // Parse Maven files
        List<Path> pomFiles = Files.walk(projectPath)
            .filter(path -> path.getFileName().toString().equals("pom.xml"))
            .collect(Collectors.toList());

        if (!pomFiles.isEmpty()) {
            MavenParser mavenParser = MavenParser.builder().build();
            mavenParser.parse(pomFiles, projectPath, executionContext)
                .forEach(sourceFiles::add);
        }

        // Parse Gradle files
        List<Path> gradleFiles = Files.walk(projectPath)
            .filter(path -> path.getFileName().toString().equals("build.gradle") ||
                           path.getFileName().toString().equals("build.gradle.kts"))
            .collect(Collectors.toList());

        if (!gradleFiles.isEmpty()) {
            GradleParser gradleParser = GradleParser.builder().build();
            gradleParser.parse(gradleFiles, projectPath, executionContext)
                .forEach(sourceFiles::add);
        }

        return sourceFiles;
    }
    
    private Environment loadCustomRecipes() throws IOException {
        // Load the custom rewrite.yml configuration
        Path rewriteConfig = Path.of("rewrite.yml");
        if (!Files.exists(rewriteConfig)) {
            logger.warn("Custom rewrite.yml not found, using default recipes");
            return Environment.builder().build();
        }
        
        String yamlContent = Files.readString(rewriteConfig);
        YamlResourceLoader yamlLoader = new YamlResourceLoader(
            new ByteArrayInputStream(yamlContent.getBytes()),
            rewriteConfig.toUri(),
            new Properties()
        );
        
        return Environment.builder()
            .load(yamlLoader)
            .build();
    }
}
