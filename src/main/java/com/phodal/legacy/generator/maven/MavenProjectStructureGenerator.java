package com.phodal.legacy.generator.maven;

import com.phodal.legacy.generator.application.GenerationConfig;
import com.phodal.legacy.services.AnalysisService;
import com.phodal.legacy.utils.LoggingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

/**
 * Generator for Maven-based Spring Boot project structure
 */
public class MavenProjectStructureGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(MavenProjectStructureGenerator.class);
    private final GenerationConfig config;
    
    public MavenProjectStructureGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate complete Maven Spring Boot project structure
     */
    public void generateProjectStructure(Path targetPath, AnalysisService.AnalysisResult analysisResult) throws IOException {
        generatePomXml(targetPath);
        generateMavenWrapper(targetPath);
        generateApplicationProperties(targetPath);
        generateApplicationYml(targetPath);
        generateGitignore(targetPath);
        generateReadme(targetPath);

        logger.info("Generated Maven Spring Boot project structure files");
    }
    
    private void generatePomXml(Path targetPath) throws IOException {
        String pomContent = generatePomXmlContent();
        Path pomPath = targetPath.resolve("pom.xml");
        Files.write(pomPath, pomContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated pom.xml");
    }
    
    private String generatePomXmlContent() {
        return String.format("""
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0" 
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     https://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <parent>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-parent</artifactId>
                    <version>%s</version>
                    <relativePath/> <!-- lookup parent from repository -->
                </parent>
                
                <groupId>%s</groupId>
                <artifactId>migrated-app</artifactId>
                <version>0.0.1-SNAPSHOT</version>
                <packaging>jar</packaging>
                <name>migrated-app</name>
                <description>Migrated Spring Boot application from JSP project</description>
                
                <properties>
                    <java.version>%s</java.version>
                    <maven.compiler.source>%s</maven.compiler.source>
                    <maven.compiler.target>%s</maven.compiler.target>
                    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
                </properties>
                
                <dependencies>
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </dependency>
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-data-jpa</artifactId>
                    </dependency>
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-validation</artifactId>
                    </dependency>
                    
                    <!-- Database -->
                    <dependency>
                        <groupId>com.h2database</groupId>
                        <artifactId>h2</artifactId>
                        <scope>runtime</scope>
                    </dependency>
                    <!-- Uncomment for MySQL -->
                    <!--
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <scope>runtime</scope>
                    </dependency>
                    -->
                    <!-- Uncomment for PostgreSQL -->
                    <!--
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <scope>runtime</scope>
                    </dependency>
                    -->
                    
                    <!-- Development tools -->
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-devtools</artifactId>
                        <scope>runtime</scope>
                        <optional>true</optional>
                    </dependency>
                    
                    <!-- Testing -->
                    <dependency>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                        <scope>test</scope>
                    </dependency>
                    
                    <!-- Additional testing dependencies -->
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter</artifactId>
                        <version>5.10.1</version>
                        <scope>test</scope>
                    </dependency>
                    <dependency>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                        <version>5.8.0</version>
                        <scope>test</scope>
                    </dependency>
                    <dependency>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-junit-jupiter</artifactId>
                        <version>5.8.0</version>
                        <scope>test</scope>
                    </dependency>
                    <dependency>
                        <groupId>org.assertj</groupId>
                        <artifactId>assertj-core</artifactId>
                        <version>3.25.1</version>
                        <scope>test</scope>
                    </dependency>
                    <dependency>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </dependency>
                </dependencies>
                
                <build>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
                            <configuration>
                                <excludes>
                                    <exclude>
                                        <groupId>org.springframework.boot</groupId>
                                        <artifactId>spring-boot-devtools</artifactId>
                                    </exclude>
                                </excludes>
                            </configuration>
                        </plugin>
                        
                        <!-- Maven Shade Plugin for fat JAR -->
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-shade-plugin</artifactId>
                            <version>3.5.1</version>
                            <executions>
                                <execution>
                                    <phase>package</phase>
                                    <goals>
                                        <goal>shade</goal>
                                    </goals>
                                    <configuration>
                                        <createDependencyReducedPom>false</createDependencyReducedPom>
                                        <transformers>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                                <mainClass>%s.Application</mainClass>
                                            </transformer>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                                <resource>META-INF/spring.handlers</resource>
                                            </transformer>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                                <resource>META-INF/spring.schemas</resource>
                                            </transformer>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                                <resource>META-INF/spring.tooling</resource>
                                            </transformer>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                                <resource>META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports</resource>
                                            </transformer>
                                            <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                                <resource>META-INF/spring/org.springframework.boot.actuate.autoconfigure.web.ManagementContextConfiguration.imports</resource>
                                            </transformer>
                                        </transformers>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                        
                        <!-- Surefire Plugin for tests -->
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <version>3.2.2</version>
                        </plugin>
                        
                        <!-- Failsafe Plugin for integration tests -->
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <version>3.2.2</version>
                        </plugin>
                    </plugins>
                </build>
            </project>
            """, 
            config.getSpringBootVersion(),
            config.getBasePackage(),
            config.getJavaVersion(),
            config.getJavaVersion(),
            config.getJavaVersion(),
            config.getBasePackage());
    }
    
    private void generateMavenWrapper(Path targetPath) throws IOException {
        // Create .mvn directory
        Path mvnDir = targetPath.resolve(".mvn/wrapper");
        Files.createDirectories(mvnDir);
        
        // Generate maven-wrapper.properties
        String wrapperPropertiesContent = """
            distributionUrl=https://repo.maven.apache.org/maven2/org/apache/maven/apache-maven/3.9.6/apache-maven-3.9.6-bin.zip
            wrapperUrl=https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper-3.2.0.jar
            """;
        
        Path wrapperPropertiesPath = mvnDir.resolve("maven-wrapper.properties");
        Files.write(wrapperPropertiesPath, wrapperPropertiesContent.getBytes(),
            StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        
        logger.info("Generated Maven wrapper files");
    }
    
    private void generateApplicationProperties(Path targetPath) throws IOException {
        String propertiesContent = String.format("""
            # Application Configuration
            spring.application.name=migrated-app
            server.servlet.context-path=%s
            
            # Database Configuration (H2 for development)
            spring.datasource.url=jdbc:h2:mem:testdb
            spring.datasource.driverClassName=org.h2.Driver
            spring.datasource.username=sa
            spring.datasource.password=
            spring.h2.console.enabled=true
            spring.h2.console.path=/h2-console
            
            # JPA Configuration
            spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
            spring.jpa.hibernate.ddl-auto=create-drop
            spring.jpa.show-sql=true
            spring.jpa.properties.hibernate.format_sql=true
            
            # Logging Configuration
            logging.level.com.example.migrated=DEBUG
            logging.level.org.springframework.web=DEBUG
            logging.level.org.hibernate.SQL=DEBUG
            
            # Development Profile
            spring.profiles.active=development
            """, config.getApiPrefix());
        
        Path propertiesPath = targetPath.resolve("src/main/resources/application.properties");
        Files.createDirectories(propertiesPath.getParent());
        Files.write(propertiesPath, propertiesContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated application.properties");
    }
    
    private void generateApplicationYml(Path targetPath) throws IOException {
        String ymlContent = String.format("""
            spring:
              application:
                name: migrated-app
              profiles:
                active: development
              datasource:
                url: jdbc:h2:mem:testdb
                driver-class-name: org.h2.Driver
                username: sa
                password: 
              h2:
                console:
                  enabled: true
                  path: /h2-console
              jpa:
                database-platform: org.hibernate.dialect.H2Dialect
                hibernate:
                  ddl-auto: create-drop
                show-sql: true
                properties:
                  hibernate:
                    format_sql: true
            
            server:
              servlet:
                context-path: %s
            
            logging:
              level:
                com.example.migrated: DEBUG
                org.springframework.web: DEBUG
                org.hibernate.SQL: DEBUG
            """, config.getApiPrefix());
        
        Path ymlPath = targetPath.resolve("src/main/resources/application.yml");
        Files.createDirectories(ymlPath.getParent());
        Files.write(ymlPath, ymlContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated application.yml");
    }
    
    private void generateGitignore(Path targetPath) throws IOException {
        String gitignoreContent = """
            HELP.md
            target/
            !.mvn/wrapper/maven-wrapper.jar
            !**/src/main/**/target/
            !**/src/test/**/target/
            
            ### STS ###
            .apt_generated
            .classpath
            .factorypath
            .project
            .settings
            .springBeans
            .sts4-cache
            
            ### IntelliJ IDEA ###
            .idea
            *.iws
            *.iml
            *.ipr
            
            ### NetBeans ###
            /nbproject/private/
            /nbbuild/
            /dist/
            /nbdist/
            /.nb-gradle/
            build/
            !**/src/main/**/build/
            !**/src/test/**/build/
            
            ### VS Code ###
            .vscode/
            
            ### Mac ###
            .DS_Store
            
            ### Logs ###
            *.log
            logs/
            
            ### Database ###
            *.db
            *.sqlite
            """;
        
        Path gitignorePath = targetPath.resolve(".gitignore");
        Files.write(gitignorePath, gitignoreContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated .gitignore");
    }
    
    private void generateReadme(Path targetPath) throws IOException {
        String readmeContent = String.format("""
            # Migrated Spring Boot Application
            
            This application was automatically generated from a legacy JSP project using the JSP to Spring Boot migration tool.
            
            ## Getting Started
            
            ### Prerequisites
            - Java %s or higher
            - Maven 3.6 or higher
            
            ### Running the Application
            
            1. Clone this repository
            2. Navigate to the project directory
            3. Run the application:
               ```bash
               ./mvnw spring-boot:run
               ```
            
            The application will start on `http://localhost:8080%s`
            
            ### Building the Application
            
            To build a fat JAR:
            ```bash
            ./mvnw clean package
            ```
            
            To run the fat JAR:
            ```bash
            java -jar target/migrated-app-0.0.1-SNAPSHOT.jar
            ```
            
            ### API Endpoints
            
            The application provides REST API endpoints at:
            - GET `%s/posts` - Get all posts
            - GET `%s/posts/{id}` - Get post by ID
            - POST `%s/posts` - Create new post
            - PUT `%s/posts/{id}` - Update post
            - DELETE `%s/posts/{id}` - Delete post
            
            ### Database Console
            
            H2 database console is available at: `http://localhost:8080%s/h2-console`
            - JDBC URL: `jdbc:h2:mem:testdb`
            - Username: `sa`
            - Password: (empty)
            
            ### Testing
            
            Run unit tests:
            ```bash
            ./mvnw test
            ```
            
            Run integration tests:
            ```bash
            ./mvnw verify
            ```
            
            ## Migration Notes
            
            This application was migrated from a JSP-based application. Some manual adjustments may be needed:
            
            1. Review and update business logic in service classes
            2. Adjust database schema and entity relationships
            3. Update API endpoints to match your requirements
            4. Configure production database settings
            5. Add authentication and authorization if needed
            
            ## Technology Stack
            
            - Spring Boot %s
            - Spring Data JPA
            - H2 Database (development)
            - Maven
            - Java %s
            """,
            config.getJavaVersion(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getSpringBootVersion(),
            config.getJavaVersion());
        
        Path readmePath = targetPath.resolve("README.md");
        Files.write(readmePath, readmeContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated README.md");
    }
}
