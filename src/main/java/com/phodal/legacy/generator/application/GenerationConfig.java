package com.phodal.legacy.generator.application;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for code generation settings.
 */
public class GenerationConfig {
    
    private String springBootVersion = "3.2.1";
    private String javaVersion = "17";
    private String basePackage = "com.example.migrated";
    private boolean generateTests = true;
    private boolean generateComments = true;
    private boolean generateJavadoc = true;
    private String codeStyle = "google";
    private boolean optimizeImports = true;
    private Map<String, String> templateOverrides = new HashMap<>();
    
    // Database configuration
    private String databaseType = "h2"; // h2, mysql, postgresql
    private boolean generateJpaEntities = true;
    private boolean generateRepositories = true;
    
    // REST API configuration
    private String apiPrefix = "/api";
    private boolean generateSwaggerDocs = true;
    private boolean enableCors = true;
    
    public GenerationConfig() {
    }
    
    public GenerationConfig(String springBootVersion, String javaVersion, String basePackage) {
        this.springBootVersion = springBootVersion;
        this.javaVersion = javaVersion;
        this.basePackage = basePackage;
    }
    
    // Getters and setters
    public String getSpringBootVersion() {
        return springBootVersion;
    }
    
    public void setSpringBootVersion(String springBootVersion) {
        this.springBootVersion = springBootVersion;
    }
    
    public String getJavaVersion() {
        return javaVersion;
    }
    
    public void setJavaVersion(String javaVersion) {
        this.javaVersion = javaVersion;
    }
    
    public String getBasePackage() {
        return basePackage;
    }
    
    public void setBasePackage(String basePackage) {
        this.basePackage = basePackage;
    }
    
    public boolean isGenerateTests() {
        return generateTests;
    }
    
    public void setGenerateTests(boolean generateTests) {
        this.generateTests = generateTests;
    }
    
    public boolean isGenerateComments() {
        return generateComments;
    }
    
    public void setGenerateComments(boolean generateComments) {
        this.generateComments = generateComments;
    }
    
    public boolean isGenerateJavadoc() {
        return generateJavadoc;
    }
    
    public void setGenerateJavadoc(boolean generateJavadoc) {
        this.generateJavadoc = generateJavadoc;
    }
    
    public String getCodeStyle() {
        return codeStyle;
    }
    
    public void setCodeStyle(String codeStyle) {
        this.codeStyle = codeStyle;
    }
    
    public boolean isOptimizeImports() {
        return optimizeImports;
    }
    
    public void setOptimizeImports(boolean optimizeImports) {
        this.optimizeImports = optimizeImports;
    }
    
    public Map<String, String> getTemplateOverrides() {
        return templateOverrides;
    }
    
    public void setTemplateOverrides(Map<String, String> templateOverrides) {
        this.templateOverrides = templateOverrides;
    }
    
    public String getDatabaseType() {
        return databaseType;
    }
    
    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }
    
    public boolean isGenerateJpaEntities() {
        return generateJpaEntities;
    }
    
    public void setGenerateJpaEntities(boolean generateJpaEntities) {
        this.generateJpaEntities = generateJpaEntities;
    }
    
    public boolean isGenerateRepositories() {
        return generateRepositories;
    }
    
    public void setGenerateRepositories(boolean generateRepositories) {
        this.generateRepositories = generateRepositories;
    }
    
    public String getApiPrefix() {
        return apiPrefix;
    }
    
    public void setApiPrefix(String apiPrefix) {
        this.apiPrefix = apiPrefix;
    }
    
    public boolean isGenerateSwaggerDocs() {
        return generateSwaggerDocs;
    }
    
    public void setGenerateSwaggerDocs(boolean generateSwaggerDocs) {
        this.generateSwaggerDocs = generateSwaggerDocs;
    }
    
    public boolean isEnableCors() {
        return enableCors;
    }
    
    public void setEnableCors(boolean enableCors) {
        this.enableCors = enableCors;
    }
}
