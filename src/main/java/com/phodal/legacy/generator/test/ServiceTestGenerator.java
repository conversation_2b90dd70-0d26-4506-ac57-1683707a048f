package com.phodal.legacy.generator.test;

import com.phodal.legacy.generator.application.GeneratedClass;
import com.phodal.legacy.generator.application.GenerationConfig;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates unit tests for service layer classes using Mockito.
 */
public class ServiceTestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceTestGenerator.class);
    
    private final GenerationConfig config;
    
    public ServiceTestGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate unit tests for all service classes
     */
    public List<GeneratedClass> generateServiceTests(List<GeneratedClass> services,
                                                     List<GeneratedClass> repositories,
                                                     List<GeneratedClass> entities,
                                                     Path targetPath) throws IOException {
        
        List<GeneratedClass> testClasses = new ArrayList<>();
        
        for (GeneratedClass service : services) {
            GeneratedClass testClass = generateServiceTest(service, repositories, entities, targetPath);
            testClasses.add(testClass);
        }
        
        logger.info("Generated {} service test classes", testClasses.size());
        return testClasses;
    }
    
    private GeneratedClass generateServiceTest(GeneratedClass service,
                                             List<GeneratedClass> repositories,
                                             List<GeneratedClass> entities,
                                             Path targetPath) throws IOException {
        
        String testClassName = service.getClassName() + "Test";
        String testPackageName = config.getBasePackage() + ".service";
        
        // Find corresponding repository and entity
        Optional<GeneratedClass> repositoryOpt = findCorrespondingRepository(service, repositories);
        Optional<GeneratedClass> entityOpt = findCorrespondingEntity(service, entities);
        
        TypeSpec.Builder testClass = TypeSpec.classBuilder(testClassName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api.extension", "ExtendWith"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.junit.jupiter.api.extension", "ExtendWith"))
                .addMember("value", "$T.class", ClassName.get("org.mockito.junit.jupiter", "MockitoExtension"))
                .build())
            .addJavadoc("Unit tests for $L", service.getClassName());
        
        // Add service under test field
        testClass.addField(FieldSpec.builder(
            ClassName.get(service.getPackageName(), service.getClassName()),
            getServiceFieldName(service.getClassName()),
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.mockito", "InjectMocks"))
            .build());
        
        // Add mocked repository field if repository exists
        if (repositoryOpt.isPresent()) {
            GeneratedClass repository = repositoryOpt.get();
            String repositoryFieldName = getRepositoryFieldName(repository.getClassName());
            
            testClass.addField(FieldSpec.builder(
                ClassName.get(repository.getPackageName(), repository.getClassName()),
                repositoryFieldName,
                Modifier.PRIVATE)
                .addAnnotation(ClassName.get("org.mockito", "Mock"))
                .build());
        }
        
        // Generate test methods
        if (entityOpt.isPresent() && repositoryOpt.isPresent()) {
            addServiceTestMethods(testClass, service, repositoryOpt.get(), entityOpt.get());
        } else {
            addBasicServiceTestMethods(testClass, service);
        }
        
        JavaFile javaFile = JavaFile.builder(testPackageName, testClass.build())
            .addFileComment("Generated unit tests for " + service.getClassName())
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(testClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(testClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST);
        generatedClass.setSourceComponent(service.getClassName());
        
        logger.info("Generated service test: {}.{}", testPackageName, testClassName);
        return generatedClass;
    }
    
    private void addServiceTestMethods(TypeSpec.Builder testClass, GeneratedClass service,
                                     GeneratedClass repository, GeneratedClass entity) {
        String entityName = entity.getClassName();
        String entityVarName = entityName.toLowerCase();
        String serviceFieldName = getServiceFieldName(service.getClassName());
        String repositoryFieldName = getRepositoryFieldName(repository.getClassName());
        
        // Test findAll method
        addFindAllTest(testClass, serviceFieldName, repositoryFieldName, entityName, entityVarName);
        
        // Test findById method
        addFindByIdTest(testClass, serviceFieldName, repositoryFieldName, entityName, entityVarName);
        
        // Test save method
        addSaveTest(testClass, serviceFieldName, repositoryFieldName, entityName, entityVarName);
        
        // Test update method
        addUpdateTest(testClass, serviceFieldName, repositoryFieldName, entityName, entityVarName);
        
        // Test deleteById method
        addDeleteByIdTest(testClass, serviceFieldName, repositoryFieldName, entityName);
    }
    
    private void addFindAllTest(TypeSpec.Builder testClass, String serviceFieldName, 
                              String repositoryFieldName, String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testFindAll")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L1 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T $L2 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T $Ls = $T.asList($L1, $L2)", 
                ParameterizedTypeName.get(ClassName.get(List.class), 
                    ClassName.get(config.getBasePackage() + ".entity", entityName)),
                entityVarName, ClassName.get(Arrays.class), entityVarName, entityVarName)
            .addStatement("$T.when($L.findAll()).thenReturn($Ls)", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName, entityVarName)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T result = $L.findAll()", 
                ParameterizedTypeName.get(ClassName.get(List.class), 
                    ClassName.get(config.getBasePackage() + ".entity", entityName)),
                serviceFieldName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertNotNull(result)", ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.assertEquals(2, result.size())", ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.verify($L).findAll()", ClassName.get("org.mockito", "Mockito"), repositoryFieldName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addFindByIdTest(TypeSpec.Builder testClass, String serviceFieldName, 
                               String repositoryFieldName, String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testFindById")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T.when($L.findById(id)).thenReturn($T.of($L))", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName, 
                ClassName.get(Optional.class), entityVarName)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T<$T> result = $L.findById(id)", 
                ClassName.get(Optional.class),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                serviceFieldName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertTrue(result.isPresent())", ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.assertEquals($L, result.get())", ClassName.get("org.junit.jupiter.api", "Assertions"), entityVarName)
            .addStatement("$T.verify($L).findById(id)", ClassName.get("org.mockito", "Mockito"), repositoryFieldName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addSaveTest(TypeSpec.Builder testClass, String serviceFieldName, 
                           String repositoryFieldName, String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testSave")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T saved$L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T.when($L.save($L)).thenReturn(saved$L)", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName, entityVarName, entityName)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T result = $L.save($L)", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                serviceFieldName, entityVarName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertNotNull(result)", ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.assertEquals(saved$L, result)", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName)
            .addStatement("$T.verify($L).save($L)", ClassName.get("org.mockito", "Mockito"), repositoryFieldName, entityVarName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addUpdateTest(TypeSpec.Builder testClass, String serviceFieldName, 
                             String repositoryFieldName, String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testUpdate")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("$T existing$L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T updated$L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T.when($L.findById(id)).thenReturn($T.of(existing$L))", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName, 
                ClassName.get(Optional.class), entityName)
            .addStatement("$T.when($L.save($T.any($T.class))).thenReturn(updated$L)", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName,
                ClassName.get("org.mockito", "ArgumentMatchers"),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T result = $L.update(id, updated$L)", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                serviceFieldName, entityName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertNotNull(result)", ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.verify($L).findById(id)", ClassName.get("org.mockito", "Mockito"), repositoryFieldName)
            .addStatement("$T.verify($L).save($T.any($T.class))", 
                ClassName.get("org.mockito", "Mockito"), repositoryFieldName,
                ClassName.get("org.mockito", "ArgumentMatchers"),
                ClassName.get(config.getBasePackage() + ".entity", entityName));
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addDeleteByIdTest(TypeSpec.Builder testClass, String serviceFieldName, 
                                 String repositoryFieldName, String entityName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testDeleteById")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$L.deleteById(id)", serviceFieldName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.verify($L).deleteById(id)", ClassName.get("org.mockito", "Mockito"), repositoryFieldName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addBasicServiceTestMethods(TypeSpec.Builder testClass, GeneratedClass service) {
        // Add a basic test method for services without clear entity/repository mapping
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testBasicFunctionality")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Test basic service functionality")
            .addStatement("$T.assertNotNull($L)", 
                ClassName.get("org.junit.jupiter.api", "Assertions"),
                getServiceFieldName(service.getClassName()));
        
        testClass.addMethod(testMethod.build());
    }
    
    private Optional<GeneratedClass> findCorrespondingRepository(GeneratedClass service, List<GeneratedClass> repositories) {
        String serviceName = service.getClassName().replace("Service", "");
        return repositories.stream()
            .filter(r -> r.getClassName().toLowerCase().contains(serviceName.toLowerCase()))
            .findFirst();
    }
    
    private Optional<GeneratedClass> findCorrespondingEntity(GeneratedClass service, List<GeneratedClass> entities) {
        String serviceName = service.getClassName().replace("Service", "");
        return entities.stream()
            .filter(e -> e.getClassName().toLowerCase().contains(serviceName.toLowerCase()))
            .findFirst();
    }
    
    private String getServiceFieldName(String serviceClassName) {
        return Character.toLowerCase(serviceClassName.charAt(0)) + serviceClassName.substring(1);
    }
    
    private String getRepositoryFieldName(String repositoryClassName) {
        return Character.toLowerCase(repositoryClassName.charAt(0)) + repositoryClassName.substring(1);
    }
}
