package com.phodal.legacy.generator.test;

import com.phodal.legacy.generator.application.GeneratedClass;
import com.phodal.legacy.generator.application.GenerationConfig;
import com.phodal.legacy.generator.application.RepositoryTestGenerator;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates comprehensive test suites for Spring Boot applications.
 * 
 * This generator creates:
 * 1. Unit tests for controllers, services, and repositories
 * 2. Integration tests for complete API workflows
 * 3. Test data builders and fixtures
 * 4. Test configuration classes
 */
public class TestSuiteGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(TestSuiteGenerator.class);
    
    private final GenerationConfig config;
    private final ControllerTestGenerator controllerTestGenerator;
    private final ServiceTestGenerator serviceTestGenerator;
    private final RepositoryTestGenerator repositoryTestGenerator;
    private final IntegrationTestGenerator integrationTestGenerator;
    private final TestDataGenerator testDataGenerator;
    
    public TestSuiteGenerator(GenerationConfig config) {
        this.config = config;
        this.controllerTestGenerator = new ControllerTestGenerator(config);
        this.serviceTestGenerator = new ServiceTestGenerator(config);
        this.repositoryTestGenerator = new RepositoryTestGenerator(config);
        this.integrationTestGenerator = new IntegrationTestGenerator(config);
        this.testDataGenerator = new TestDataGenerator(config);
    }
    
    /**
     * Generate complete test suite for the Spring Boot application
     */
    public List<GeneratedClass> generateTestSuite(List<GeneratedClass> entities,
                                                  List<GeneratedClass> repositories,
                                                  List<GeneratedClass> services,
                                                  List<GeneratedClass> controllers,
                                                  Path targetPath) throws IOException {
        
        List<GeneratedClass> testClasses = new ArrayList<>();
        
        // Create test directory structure
        createTestDirectoryStructure(targetPath);
        
        // Generate test data builders and fixtures
        List<GeneratedClass> testDataClasses = testDataGenerator.generateTestData(entities, targetPath);
        testClasses.addAll(testDataClasses);
        
        // Generate repository tests
        List<GeneratedClass> repositoryTests = repositoryTestGenerator.generateRepositoryTests(
            repositories, entities, targetPath);
        testClasses.addAll(repositoryTests);
        
        // Generate service tests
        List<GeneratedClass> serviceTests = serviceTestGenerator.generateServiceTests(
            services, repositories, entities, targetPath);
        testClasses.addAll(serviceTests);
        
        // Generate controller tests
        List<GeneratedClass> controllerTests = controllerTestGenerator.generateControllerTests(
            controllers, services, entities, targetPath);
        testClasses.addAll(controllerTests);
        
        // Generate integration tests
        List<GeneratedClass> integrationTests = integrationTestGenerator.generateIntegrationTests(
            controllers, entities, targetPath);
        testClasses.addAll(integrationTests);
        
        // Generate test configuration
        GeneratedClass testConfig = generateTestConfiguration(targetPath);
        testClasses.add(testConfig);
        
        // Generate test application properties
        generateTestApplicationProperties(targetPath);
        
        logger.info("Generated {} test classes", testClasses.size());
        return testClasses;
    }
    
    private void createTestDirectoryStructure(Path targetPath) throws IOException {
        String packagePath = config.getBasePackage().replace(".", "/");
        Path testJavaPath = targetPath.resolve("src/test/java").resolve(packagePath);
        
        // Create test package directories
        java.nio.file.Files.createDirectories(testJavaPath.resolve("controller"));
        java.nio.file.Files.createDirectories(testJavaPath.resolve("service"));
        java.nio.file.Files.createDirectories(testJavaPath.resolve("repository"));
        java.nio.file.Files.createDirectories(testJavaPath.resolve("integration"));
        java.nio.file.Files.createDirectories(testJavaPath.resolve("testdata"));
        java.nio.file.Files.createDirectories(testJavaPath.resolve("config"));
        
        // Create test resources directory
        java.nio.file.Files.createDirectories(targetPath.resolve("src/test/resources"));
        
        logger.info("Created test directory structure");
    }
    
    private GeneratedClass generateTestConfiguration(Path targetPath) throws IOException {
        String className = "TestConfiguration";
        String packageName = config.getBasePackage() + ".config";
        
        TypeSpec.Builder testConfigClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.test.context", "TestConfiguration"))
            .addAnnotation(ClassName.get("org.springframework.context.annotation", "Profile"))
            .addJavadoc("Test configuration for Spring Boot application tests");
        
        // Add test profile configuration
        testConfigClass.addMethod(MethodSpec.methodBuilder("testDataSource")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.context.annotation", "Bean"))
            .addAnnotation(ClassName.get("org.springframework.context.annotation", "Primary"))
            .returns(ClassName.get("javax.sql.DataSource", "DataSource"))
            .addStatement("return new $T()", ClassName.get("org.springframework.jdbc.datasource", "EmbeddedDatabaseBuilder"))
            .addStatement("    .setType($T.H2)", ClassName.get("org.springframework.jdbc.datasource.embedded", "EmbeddedDatabaseType"))
            .addStatement("    .build()")
            .build());
        
        JavaFile javaFile = JavaFile.builder(packageName, testConfigClass.build())
            .addFileComment("Generated test configuration")
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST_CONFIG);
        
        logger.info("Generated test configuration: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private void generateTestApplicationProperties(Path targetPath) throws IOException {
        String testPropertiesContent = """
            # Test Application Configuration
            spring.application.name=migrated-app-test
            
            # Test Database Configuration
            spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
            spring.datasource.driverClassName=org.h2.Driver
            spring.datasource.username=sa
            spring.datasource.password=
            
            # JPA Test Configuration
            spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
            spring.jpa.hibernate.ddl-auto=create-drop
            spring.jpa.show-sql=false
            spring.jpa.properties.hibernate.format_sql=false
            
            # Disable H2 console in tests
            spring.h2.console.enabled=false
            
            # Test Logging Configuration
            logging.level.org.springframework.web=WARN
            logging.level.org.hibernate.SQL=WARN
            logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
            
            # Test Profile
            spring.profiles.active=test
            """;
        
        Path testPropertiesPath = targetPath.resolve("src/test/resources/application-test.properties");
        java.nio.file.Files.createDirectories(testPropertiesPath.getParent());
        java.nio.file.Files.write(testPropertiesPath, testPropertiesContent.getBytes());
        
        logger.info("Generated test application properties");
    }
}
