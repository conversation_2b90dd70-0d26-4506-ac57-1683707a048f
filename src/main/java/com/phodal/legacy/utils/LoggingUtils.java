package com.phodal.legacy.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Utility class for enhanced logging functionality throughout the application.
 * Provides structured logging, progress tracking, and contextual information.
 */
public class LoggingUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingUtils.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // MDC keys for structured logging
    public static final String OPERATION_KEY = "operation";
    public static final String FILE_PATH_KEY = "filePath";
    public static final String COMPONENT_KEY = "component";
    public static final String PHASE_KEY = "phase";
    
    /**
     * Log the start of an operation with context
     */
    public static void logOperationStart(String operation, String component) {
        MDC.put(OPERATION_KEY, operation);
        MDC.put(COMPONENT_KEY, component);
        MDC.put(PHASE_KEY, "START");
        
        logger.info("🚀 Starting operation: {} in component: {}", operation, component);
    }
    
    /**
     * Log the completion of an operation
     */
    public static void logOperationComplete(String operation, String component) {
        MDC.put(PHASE_KEY, "COMPLETE");
        logger.info("✅ Completed operation: {} in component: {}", operation, component);
        
        // Clear MDC for this operation
        MDC.remove(OPERATION_KEY);
        MDC.remove(COMPONENT_KEY);
        MDC.remove(PHASE_KEY);
    }
    
    /**
     * Log operation failure with error details
     */
    public static void logOperationError(String operation, String component, Throwable error) {
        MDC.put(PHASE_KEY, "ERROR");
        logger.error("❌ Failed operation: {} in component: {} - Error: {}", 
                    operation, component, error.getMessage(), error);
        
        // Clear MDC for this operation
        MDC.remove(OPERATION_KEY);
        MDC.remove(COMPONENT_KEY);
        MDC.remove(PHASE_KEY);
    }
    
    /**
     * Log file processing with path context
     */
    public static void logFileProcessing(String filePath, String action) {
        MDC.put(FILE_PATH_KEY, filePath);
        logger.info("📁 {} file: {}", action, filePath);
    }
    
    /**
     * Log file processing completion
     */
    public static void logFileProcessed(String filePath, String action) {
        logger.info("✅ {} file completed: {}", action, filePath);
        MDC.remove(FILE_PATH_KEY);
    }
    
    /**
     * Log progress with percentage and details
     */
    public static void logProgress(int current, int total, String description) {
        double percentage = (double) current / total * 100;
        logger.info("📊 Progress: {}/{} ({:.1f}%) - {}", current, total, percentage, description);
    }
    
    /**
     * Log analysis results with structured data
     */
    public static void logAnalysisResult(String analysisType, Map<String, Object> results) {
        logger.info("🔍 Analysis completed: {}", analysisType);
        results.forEach((key, value) -> 
            logger.info("   {} = {}", key, value));
    }
    
    /**
     * Log code generation activity
     */
    public static void logCodeGeneration(String generationType, String targetPath) {
        logger.info("🔧 Generating {}: {}", generationType, targetPath);
    }
    
    /**
     * Log validation results
     */
    public static void logValidation(String validationType, boolean passed, String details) {
        String emoji = passed ? "✅" : "❌";
        String status = passed ? "PASSED" : "FAILED";
        logger.info("{} Validation {}: {} - {}", emoji, status, validationType, details);
    }
    
    /**
     * Log migration step with phase information
     */
    public static void logMigrationStep(String step, String phase, String details) {
        logger.info("🔄 Migration Step [{}]: {} - {}", phase, step, details);
    }
    
    /**
     * Log dependency information
     */
    public static void logDependency(String dependencyType, String name, String version) {
        logger.info("📦 Dependency {}: {} (version: {})", dependencyType, name, version);
    }
    
    /**
     * Log configuration changes
     */
    public static void logConfigChange(String configType, String property, String oldValue, String newValue) {
        logger.info("⚙️  Config change in {}: {} = {} → {}", configType, property, oldValue, newValue);
    }
    
    /**
     * Log warning with context
     */
    public static void logWarning(String component, String message, String suggestion) {
        logger.warn("⚠️  Warning in {}: {} | Suggestion: {}", component, message, suggestion);
    }
    
    /**
     * Log summary information
     */
    public static void logSummary(String operation, Map<String, Object> summary) {
        logger.info("📋 Summary for {}: ", operation);
        summary.forEach((key, value) -> 
            logger.info("   {} = {}", key, value));
    }
    
    /**
     * Create a timestamped log entry
     */
    public static void logTimestamped(String message) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        logger.info("[{}] {}", timestamp, message);
    }
    
    /**
     * Log performance metrics
     */
    public static void logPerformance(String operation, long durationMs, String additionalInfo) {
        logger.info("⏱️  Performance: {} took {}ms - {}", operation, durationMs, additionalInfo);
    }
    
    /**
     * Clear all MDC context
     */
    public static void clearContext() {
        MDC.clear();
    }
    
    /**
     * Set custom context for logging
     */
    public static void setContext(String key, String value) {
        MDC.put(key, value);
    }
    
    /**
     * Get current context value
     */
    public static String getContext(String key) {
        return MDC.get(key);
    }
}
