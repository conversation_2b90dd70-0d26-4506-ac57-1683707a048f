package com.phodal.legacy.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * Utility class to detect the build system type of a project
 */
public class ProjectTypeDetector {
    
    private static final Logger logger = LoggerFactory.getLogger(ProjectTypeDetector.class);
    
    public enum BuildSystem {
        MAVEN,
        GRADLE,
        UNKNOWN
    }
    
    /**
     * Detect the build system of a project
     */
    public static BuildSystem detectBuildSystem(Path projectRoot) {
        LoggingUtils.logOperationStart("detectBuildSystem", "ProjectTypeDetector");
        
        try {
            // Check for Maven files
            if (Files.exists(projectRoot.resolve("pom.xml"))) {
                logger.info("Detected Maven project (pom.xml found)");
                LoggingUtils.logOperationComplete("detectBuildSystem", "ProjectTypeDetector");
                return BuildSystem.MAVEN;
            }
            
            // Check for Gradle files
            if (Files.exists(projectRoot.resolve("build.gradle")) || 
                Files.exists(projectRoot.resolve("build.gradle.kts"))) {
                logger.info("Detected Gradle project (build.gradle found)");
                LoggingUtils.logOperationComplete("detectBuildSystem", "ProjectTypeDetector");
                return BuildSystem.GRADLE;
            }
            
            // Check for Gradle wrapper
            if (Files.exists(projectRoot.resolve("gradlew")) || 
                Files.exists(projectRoot.resolve("gradlew.bat"))) {
                logger.info("Detected Gradle project (gradle wrapper found)");
                LoggingUtils.logOperationComplete("detectBuildSystem", "ProjectTypeDetector");
                return BuildSystem.GRADLE;
            }
            
            logger.warn("Could not detect build system for project: {}", projectRoot);
            LoggingUtils.logOperationComplete("detectBuildSystem", "ProjectTypeDetector");
            return BuildSystem.UNKNOWN;
            
        } catch (Exception e) {
            logger.error("Error detecting build system: {}", e.getMessage());
            LoggingUtils.logOperationError("detectBuildSystem", "ProjectTypeDetector", e);
            return BuildSystem.UNKNOWN;
        }
    }
    
    /**
     * Get the preferred build system for migration
     * If original is Maven, preserve it; otherwise default to Gradle
     */
    public static BuildSystem getPreferredBuildSystem(Path projectRoot, boolean preserveOriginal) {
        if (!preserveOriginal) {
            return BuildSystem.GRADLE; // Default to Gradle for new projects
        }
        
        BuildSystem detected = detectBuildSystem(projectRoot);
        if (detected == BuildSystem.UNKNOWN) {
            logger.info("Unknown build system, defaulting to Maven for legacy projects");
            return BuildSystem.MAVEN;
        }
        
        return detected;
    }
    
    /**
     * Check if project has Maven structure
     */
    public static boolean hasMavenStructure(Path projectRoot) {
        return Files.exists(projectRoot.resolve("src/main/java")) &&
               Files.exists(projectRoot.resolve("src/main/webapp")) &&
               Files.exists(projectRoot.resolve("pom.xml"));
    }
    
    /**
     * Check if project has Gradle structure
     */
    public static boolean hasGradleStructure(Path projectRoot) {
        return Files.exists(projectRoot.resolve("src/main/java")) &&
               (Files.exists(projectRoot.resolve("build.gradle")) || 
                Files.exists(projectRoot.resolve("build.gradle.kts")));
    }
    
    /**
     * Get the Java version from Maven pom.xml
     */
    public static String detectJavaVersionFromMaven(Path projectRoot) {
        Path pomPath = projectRoot.resolve("pom.xml");
        if (!Files.exists(pomPath)) {
            return "17"; // Default
        }
        
        try {
            String content = Files.readString(pomPath);
            
            // Look for maven.compiler.source
            if (content.contains("<maven.compiler.source>")) {
                String version = extractXmlValue(content, "maven.compiler.source");
                if (version != null) {
                    return version;
                }
            }
            
            // Look for source configuration in compiler plugin
            if (content.contains("<source>")) {
                String version = extractXmlValue(content, "source");
                if (version != null) {
                    return version;
                }
            }
            
            // Default to Java 8 for legacy projects
            return "8";
            
        } catch (IOException e) {
            logger.warn("Could not read pom.xml to detect Java version: {}", e.getMessage());
            return "17";
        }
    }
    
    private static String extractXmlValue(String content, String tagName) {
        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";
        
        int startIndex = content.indexOf(startTag);
        if (startIndex == -1) return null;
        
        startIndex += startTag.length();
        int endIndex = content.indexOf(endTag, startIndex);
        if (endIndex == -1) return null;
        
        return content.substring(startIndex, endIndex).trim();
    }
}
