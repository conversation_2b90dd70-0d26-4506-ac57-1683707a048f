package com.phodal.legacy.utils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Utility class for file operations throughout the migration process.
 * Provides safe file handling, directory traversal, and content manipulation.
 */
public class FileUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);
    
    // Common file extensions for JSP projects
    public static final Set<String> JSP_EXTENSIONS = Set.of(".jsp", ".jspx", ".tag", ".tagx");
    public static final Set<String> JAVA_EXTENSIONS = Set.of(".java");
    public static final Set<String> WEB_XML_FILES = Set.of("web.xml");
    public static final Set<String> CONFIG_EXTENSIONS = Set.of(".xml", ".properties", ".yml", ".yaml");
    
    /**
     * Safely read file content as string
     */
    public static String readFileContent(Path filePath) throws IOException {
        LoggingUtils.logFileProcessing(filePath.toString(), "Reading");
        
        if (!Files.exists(filePath)) {
            throw new FileNotFoundException("File not found: " + filePath);
        }
        
        try {
            String content = Files.readString(filePath, StandardCharsets.UTF_8);
            LoggingUtils.logFileProcessed(filePath.toString(), "Read");
            return content;
        } catch (IOException e) {
            LoggingUtils.logOperationError("readFile", "FileUtils", e);
            throw e;
        }
    }
    
    /**
     * Safely write content to file
     */
    public static void writeFileContent(Path filePath, String content) throws IOException {
        LoggingUtils.logFileProcessing(filePath.toString(), "Writing");
        
        // Create parent directories if they don't exist
        Path parentDir = filePath.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
            logger.debug("Created directory: {}", parentDir);
        }
        
        try {
            Files.writeString(filePath, content, StandardCharsets.UTF_8, 
                            StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            LoggingUtils.logFileProcessed(filePath.toString(), "Written");
        } catch (IOException e) {
            LoggingUtils.logOperationError("writeFile", "FileUtils", e);
            throw e;
        }
    }
    
    /**
     * Copy file from source to destination
     */
    public static void copyFile(Path source, Path destination) throws IOException {
        LoggingUtils.logFileProcessing(source.toString(), "Copying to " + destination);
        
        // Create parent directories if they don't exist
        Path parentDir = destination.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        
        try {
            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
            LoggingUtils.logFileProcessed(destination.toString(), "Copied");
        } catch (IOException e) {
            LoggingUtils.logOperationError("copyFile", "FileUtils", e);
            throw e;
        }
    }
    
    /**
     * Find all files with specific extensions in a directory tree
     */
    public static List<Path> findFilesByExtensions(Path rootDir, Set<String> extensions) throws IOException {
        LoggingUtils.logOperationStart("findFilesByExtensions", "FileUtils");
        
        if (!Files.exists(rootDir) || !Files.isDirectory(rootDir)) {
            throw new IllegalArgumentException("Root directory does not exist or is not a directory: " + rootDir);
        }
        
        List<Path> foundFiles = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(rootDir)) {
            foundFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return extensions.stream().anyMatch(fileName::endsWith);
                })
                .collect(Collectors.toList());
        } catch (IOException e) {
            LoggingUtils.logOperationError("findFilesByExtensions", "FileUtils", e);
            throw e;
        }
        
        logger.info("Found {} files with extensions {} in {}", 
                   foundFiles.size(), extensions, rootDir);
        LoggingUtils.logOperationComplete("findFilesByExtensions", "FileUtils");
        
        return foundFiles;
    }
    
    /**
     * Find all JSP files in a directory tree
     */
    public static List<Path> findJspFiles(Path rootDir) throws IOException {
        return findFilesByExtensions(rootDir, JSP_EXTENSIONS);
    }
    
    /**
     * Find all Java files in a directory tree
     */
    public static List<Path> findJavaFiles(Path rootDir) throws IOException {
        return findFilesByExtensions(rootDir, JAVA_EXTENSIONS);
    }
    
    /**
     * Find web.xml files in a directory tree
     */
    public static List<Path> findWebXmlFiles(Path rootDir) throws IOException {
        LoggingUtils.logOperationStart("findWebXmlFiles", "FileUtils");
        
        List<Path> webXmlFiles = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(rootDir)) {
            webXmlFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> WEB_XML_FILES.contains(path.getFileName().toString().toLowerCase()))
                .collect(Collectors.toList());
        } catch (IOException e) {
            LoggingUtils.logOperationError("findWebXmlFiles", "FileUtils", e);
            throw e;
        }
        
        logger.info("Found {} web.xml files in {}", webXmlFiles.size(), rootDir);
        LoggingUtils.logOperationComplete("findWebXmlFiles", "FileUtils");
        
        return webXmlFiles;
    }
    
    /**
     * Create directory structure if it doesn't exist
     */
    public static void ensureDirectoryExists(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
            logger.debug("Created directory: {}", directory);
        }
    }
    
    /**
     * Get file extension (including the dot)
     */
    public static String getFileExtension(Path filePath) {
        String fileName = filePath.getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }
    
    /**
     * Get file name without extension
     */
    public static String getFileNameWithoutExtension(Path filePath) {
        String fileName = filePath.getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }
    
    /**
     * Check if file is a JSP file
     */
    public static boolean isJspFile(Path filePath) {
        String extension = getFileExtension(filePath).toLowerCase();
        return JSP_EXTENSIONS.contains(extension);
    }
    
    /**
     * Check if file is a Java file
     */
    public static boolean isJavaFile(Path filePath) {
        String extension = getFileExtension(filePath).toLowerCase();
        return JAVA_EXTENSIONS.contains(extension);
    }
    
    /**
     * Get relative path from base directory
     */
    public static String getRelativePath(Path basePath, Path targetPath) {
        try {
            return basePath.relativize(targetPath).toString();
        } catch (IllegalArgumentException e) {
            // If paths are not related, return absolute path
            return targetPath.toString();
        }
    }
    
    /**
     * Calculate directory size recursively
     */
    public static long calculateDirectorySize(Path directory) throws IOException {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            return 0;
        }
        
        try (Stream<Path> paths = Files.walk(directory)) {
            return paths
                .filter(Files::isRegularFile)
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        logger.warn("Could not get size of file: {}", path);
                        return 0;
                    }
                })
                .sum();
        }
    }
    
    /**
     * Count files in directory by type
     */
    public static Map<String, Integer> countFilesByExtension(Path directory) throws IOException {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            return Collections.emptyMap();
        }
        
        Map<String, Integer> counts = new HashMap<>();
        
        try (Stream<Path> paths = Files.walk(directory)) {
            paths
                .filter(Files::isRegularFile)
                .forEach(path -> {
                    String extension = getFileExtension(path).toLowerCase();
                    if (extension.isEmpty()) {
                        extension = "no-extension";
                    }
                    counts.merge(extension, 1, Integer::sum);
                });
        }
        
        return counts;
    }
    
    /**
     * Backup file with timestamp
     */
    public static Path backupFile(Path originalFile) throws IOException {
        if (!Files.exists(originalFile)) {
            throw new FileNotFoundException("File to backup does not exist: " + originalFile);
        }
        
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = originalFile.getFileName().toString();
        String backupFileName = fileName + ".backup." + timestamp;
        Path backupPath = originalFile.getParent().resolve(backupFileName);
        
        Files.copy(originalFile, backupPath);
        logger.info("Created backup: {}", backupPath);
        
        return backupPath;
    }
}
