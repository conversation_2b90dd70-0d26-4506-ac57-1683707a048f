package com.phodal.legacy.model;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Registry for managing JSP to bytecode mappings.
 * Provides centralized storage and retrieval of mapping relationships.
 */
public class MappingRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(MappingRegistry.class);
    
    private final Map<String, JspBytecodeMapping> jspMappings;
    private final Map<String, Set<String>> classToJspMappings;
    private final Map<String, Set<String>> dependencyMappings;
    private final Map<String, MappingStatistics> statistics;
    
    public MappingRegistry() {
        this.jspMappings = new ConcurrentHashMap<>();
        this.classToJspMappings = new ConcurrentHashMap<>();
        this.dependencyMappings = new ConcurrentHashMap<>();
        this.statistics = new ConcurrentHashMap<>();
    }
    
    /**
     * Register a JSP to bytecode mapping
     */
    public void registerMapping(JspBytecodeMapping mapping) {
        String jspId = mapping.getJspComponentId();
        
        // Store the main mapping
        jspMappings.put(jspId, mapping);
        
        // Build reverse mappings for quick lookup
        if (mapping.getCompiledServletClass() != null) {
            classToJspMappings.computeIfAbsent(mapping.getCompiledServletClass(), k -> new HashSet<>())
                             .add(jspId);
        }
        
        // Build dependency reverse mappings
        for (String dependency : mapping.getDependencyMappings().values()) {
            dependencyMappings.computeIfAbsent(dependency, k -> new HashSet<>())
                             .add(jspId);
        }
        
        logger.debug("Registered mapping for JSP: {} -> {}", mapping.getJspPath(), mapping.getCompiledServletClass());
    }
    
    /**
     * Get mapping by JSP component ID
     */
    public JspBytecodeMapping getMapping(String jspComponentId) {
        return jspMappings.get(jspComponentId);
    }
    
    /**
     * Get mapping by JSP path
     */
    public JspBytecodeMapping getMappingByPath(String jspPath) {
        return jspMappings.values().stream()
                         .filter(mapping -> jspPath.equals(mapping.getJspPath()))
                         .findFirst()
                         .orElse(null);
    }
    
    /**
     * Get all JSP mappings that use a specific bytecode class
     */
    public Set<JspBytecodeMapping> getMappingsByBytecodeClass(String className) {
        Set<String> jspIds = classToJspMappings.get(className);
        if (jspIds == null) {
            return Collections.emptySet();
        }
        
        return jspIds.stream()
                    .map(jspMappings::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
    }
    
    /**
     * Get all JSP mappings that depend on a specific class
     */
    public Set<JspBytecodeMapping> getMappingsByDependency(String dependency) {
        Set<String> jspIds = dependencyMappings.get(dependency);
        if (jspIds == null) {
            return Collections.emptySet();
        }
        
        return jspIds.stream()
                    .map(jspMappings::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
    }
    
    /**
     * Get all mappings
     */
    public Collection<JspBytecodeMapping> getAllMappings() {
        return Collections.unmodifiableCollection(jspMappings.values());
    }
    
    /**
     * Remove a mapping
     */
    public boolean removeMapping(String jspComponentId) {
        JspBytecodeMapping mapping = jspMappings.remove(jspComponentId);
        if (mapping == null) {
            return false;
        }
        
        // Clean up reverse mappings
        if (mapping.getCompiledServletClass() != null) {
            Set<String> jspIds = classToJspMappings.get(mapping.getCompiledServletClass());
            if (jspIds != null) {
                jspIds.remove(jspComponentId);
                if (jspIds.isEmpty()) {
                    classToJspMappings.remove(mapping.getCompiledServletClass());
                }
            }
        }
        
        // Clean up dependency mappings
        for (String dependency : mapping.getDependencyMappings().values()) {
            Set<String> jspIds = dependencyMappings.get(dependency);
            if (jspIds != null) {
                jspIds.remove(jspComponentId);
                if (jspIds.isEmpty()) {
                    dependencyMappings.remove(dependency);
                }
            }
        }
        
        logger.debug("Removed mapping for JSP: {}", mapping.getJspPath());
        return true;
    }
    
    /**
     * Clear all mappings
     */
    public void clear() {
        jspMappings.clear();
        classToJspMappings.clear();
        dependencyMappings.clear();
        statistics.clear();
        logger.debug("Cleared all mappings");
    }
    
    /**
     * Get mapping statistics
     */
    public MappingStatistics getStatistics() {
        MappingStatistics stats = new MappingStatistics();
        stats.totalMappings = jspMappings.size();
        stats.totalBytecodeClasses = classToJspMappings.size();
        stats.totalDependencies = dependencyMappings.size();
        
        // Calculate detailed statistics
        for (JspBytecodeMapping mapping : jspMappings.values()) {
            stats.totalClassMappings += mapping.getClassNameMappings().size();
            stats.totalMethodMappings += mapping.getMethodMappings().size();
            stats.totalDependencyMappings += mapping.getDependencyMappings().size();
            stats.totalTagLibraryMappings += mapping.getTagLibraryMappings().size();
        }
        
        return stats;
    }
    
    /**
     * Check if a JSP has mapping
     */
    public boolean hasMapping(String jspComponentId) {
        return jspMappings.containsKey(jspComponentId);
    }
    
    /**
     * Check if a bytecode class is mapped
     */
    public boolean isBytecodeClassMapped(String className) {
        return classToJspMappings.containsKey(className);
    }
    
    /**
     * Get all mapped bytecode classes
     */
    public Set<String> getMappedBytecodeClasses() {
        return Collections.unmodifiableSet(classToJspMappings.keySet());
    }
    
    /**
     * Get all mapped dependencies
     */
    public Set<String> getMappedDependencies() {
        return Collections.unmodifiableSet(dependencyMappings.keySet());
    }
    
    @Override
    public String toString() {
        MappingStatistics stats = getStatistics();
        return String.format("MappingRegistry{mappings=%d, classes=%d, dependencies=%d}",
                           stats.totalMappings, stats.totalBytecodeClasses, stats.totalDependencies);
    }
    
    /**
     * Statistics container for mapping registry
     */
    public static class MappingStatistics {
        public int totalMappings;
        public int totalBytecodeClasses;
        public int totalDependencies;
        public int totalClassMappings;
        public int totalMethodMappings;
        public int totalDependencyMappings;
        public int totalTagLibraryMappings;
        
        @Override
        public String toString() {
            return String.format("MappingStatistics{mappings=%d, bytecodeClasses=%d, dependencies=%d, " +
                               "classMappings=%d, methodMappings=%d, dependencyMappings=%d, tagLibMappings=%d}",
                               totalMappings, totalBytecodeClasses, totalDependencies,
                               totalClassMappings, totalMethodMappings, totalDependencyMappings, totalTagLibraryMappings);
        }
    }
}
