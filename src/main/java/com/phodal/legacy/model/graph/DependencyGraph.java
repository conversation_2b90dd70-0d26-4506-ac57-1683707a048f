package com.phodal.legacy.model.graph;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Represents the dependency graph for a legacy JSP application.
 * Manages relationships between components and provides analysis capabilities.
 */
public class DependencyGraph {
    
    private static final Logger logger = LoggerFactory.getLogger(DependencyGraph.class);
    
    private Map<String, GraphNode> nodes;
    private Map<String, ComponentModel> components;
    private boolean isBuilt;
    
    public DependencyGraph() {
        this.nodes = new HashMap<>();
        this.components = new HashMap<>();
        this.isBuilt = false;
    }
    
    /**
     * Add a component to the graph
     */
    public void addComponent(ComponentModel component) {
        components.put(component.getId(), component);
        nodes.put(component.getId(), new GraphNode(component));
        isBuilt = false; // Mark as needing rebuild
    }
    
    /**
     * Add multiple components to the graph
     */
    public void addComponents(List<ComponentModel> componentList) {
        for (ComponentModel component : componentList) {
            addComponent(component);
        }
    }
    
    /**
     * Build the dependency relationships between nodes
     */
    public void buildDependencies() {
        logger.info("Building dependency relationships for {} components", components.size());
        
        for (ComponentModel component : components.values()) {
            GraphNode node = nodes.get(component.getId());
            
            // Build dependencies based on component dependencies
            for (String dependencyId : component.getDependencies()) {
                GraphNode dependencyNode = findNodeByDependencyId(dependencyId);
                if (dependencyNode != null) {
                    node.addDependency(dependencyNode);
                }
            }
        }
        
        isBuilt = true;
        logger.info("Dependency graph built successfully");
    }
    
    /**
     * Get a node by component ID
     */
    public GraphNode getNode(String componentId) {
        return nodes.get(componentId);
    }
    
    /**
     * Get all nodes in the graph
     */
    public Collection<GraphNode> getAllNodes() {
        return nodes.values();
    }
    
    /**
     * Get all components in the graph
     */
    public Collection<ComponentModel> getAllComponents() {
        return components.values();
    }
    
    /**
     * Get nodes by component type
     */
    public List<GraphNode> getNodesByType(ComponentModel.ComponentType type) {
        return nodes.values().stream()
            .filter(node -> node.getComponent().getType() == type)
            .collect(Collectors.toList());
    }
    
    /**
     * Get root nodes (nodes with no dependencies)
     */
    public List<GraphNode> getRootNodes() {
        return nodes.values().stream()
            .filter(GraphNode::isRoot)
            .collect(Collectors.toList());
    }
    
    /**
     * Get leaf nodes (nodes with no dependents)
     */
    public List<GraphNode> getLeafNodes() {
        return nodes.values().stream()
            .filter(GraphNode::isLeaf)
            .collect(Collectors.toList());
    }
    
    /**
     * Perform topological sort to get migration order
     */
    public List<GraphNode> getTopologicalOrder() {
        if (!isBuilt) {
            buildDependencies();
        }
        
        List<GraphNode> result = new ArrayList<>();
        Set<GraphNode> visited = new HashSet<>();
        Set<GraphNode> visiting = new HashSet<>();
        
        for (GraphNode node : nodes.values()) {
            if (!visited.contains(node)) {
                if (!topologicalSortDFS(node, visited, visiting, result)) {
                    logger.warn("Circular dependency detected in graph");
                    return getTopologicalOrderWithCycleBreaking();
                }
            }
        }
        
        return result;
    }
    
    /**
     * Get migration order considering priorities
     */
    public List<GraphNode> getMigrationOrder() {
        List<GraphNode> topologicalOrder = getTopologicalOrder();
        
        // Group by depth and sort by priority within each depth
        Map<Integer, List<GraphNode>> depthGroups = new HashMap<>();
        calculateDepths();
        
        for (GraphNode node : topologicalOrder) {
            int depth = node.getDepth();
            depthGroups.computeIfAbsent(depth, k -> new ArrayList<>()).add(node);
        }
        
        List<GraphNode> migrationOrder = new ArrayList<>();
        for (int depth = 0; depth <= getMaxDepth(); depth++) {
            List<GraphNode> nodesAtDepth = depthGroups.get(depth);
            if (nodesAtDepth != null) {
                // Sort by migration priority
                nodesAtDepth.sort((a, b) -> Integer.compare(
                    b.calculateMigrationPriority(), 
                    a.calculateMigrationPriority()
                ));
                migrationOrder.addAll(nodesAtDepth);
            }
        }
        
        return migrationOrder;
    }
    
    /**
     * Detect circular dependencies
     */
    public List<List<GraphNode>> detectCircularDependencies() {
        List<List<GraphNode>> cycles = new ArrayList<>();
        Set<GraphNode> visited = new HashSet<>();
        Set<GraphNode> visiting = new HashSet<>();
        
        for (GraphNode node : nodes.values()) {
            if (!visited.contains(node)) {
                List<GraphNode> path = new ArrayList<>();
                detectCyclesDFS(node, visited, visiting, path, cycles);
            }
        }
        
        return cycles;
    }
    
    /**
     * Calculate depths for all nodes
     */
    public void calculateDepths() {
        // Reset depths
        for (GraphNode node : nodes.values()) {
            node.setDepth(-1);
        }
        
        // Calculate depths starting from root nodes
        List<GraphNode> rootNodes = getRootNodes();
        for (GraphNode root : rootNodes) {
            calculateDepthDFS(root, 0);
        }
    }
    
    /**
     * Get the maximum depth in the graph
     */
    public int getMaxDepth() {
        return nodes.values().stream()
            .mapToInt(GraphNode::getDepth)
            .max()
            .orElse(0);
    }
    
    /**
     * Get graph statistics
     */
    public GraphStatistics getStatistics() {
        if (!isBuilt) {
            buildDependencies();
        }
        
        GraphStatistics stats = new GraphStatistics();
        stats.totalNodes = nodes.size();
        stats.rootNodes = getRootNodes().size();
        stats.leafNodes = getLeafNodes().size();
        stats.maxDepth = getMaxDepth();
        stats.circularDependencies = detectCircularDependencies().size();
        
        // Count by component type
        for (GraphNode node : nodes.values()) {
            ComponentModel.ComponentType type = node.getComponent().getType();
            stats.componentTypeCounts.merge(type, 1, Integer::sum);
        }
        
        return stats;
    }
    
    // Private helper methods
    private GraphNode findNodeByDependencyId(String dependencyId) {
        // First try exact match by component ID
        GraphNode node = nodes.get(dependencyId);
        if (node != null) {
            return node;
        }

        // Try to find by component name, source path, or class name
        for (GraphNode candidate : nodes.values()) {
            ComponentModel component = candidate.getComponent();

            // Check if dependency matches component ID, name, or source path
            if (dependencyId.equals(component.getId()) ||
                dependencyId.equals(component.getName()) ||
                dependencyId.equals(component.getSourcePath())) {
                return candidate;
            }

            // For class dependencies, try to match by class name
            if (dependencyId.contains(".")) {
                String className = extractClassName(component.getSourcePath());
                if (dependencyId.endsWith(className)) {
                    return candidate;
                }

                // Also try matching full class name from properties
                Object packageName = component.getProperty("packageName");
                if (packageName != null) {
                    String fullClassName = packageName + "." + className;
                    if (dependencyId.equals(fullClassName)) {
                        return candidate;
                    }
                }
            }
        }

        return null;
    }
    
    private String extractClassName(String sourcePath) {
        if (sourcePath == null) return "";
        
        String fileName = sourcePath.substring(sourcePath.lastIndexOf('/') + 1);
        if (fileName.endsWith(".java")) {
            return fileName.substring(0, fileName.length() - 5);
        }
        return fileName;
    }
    
    private boolean topologicalSortDFS(GraphNode node, Set<GraphNode> visited, 
                                     Set<GraphNode> visiting, List<GraphNode> result) {
        if (visiting.contains(node)) {
            return false; // Cycle detected
        }
        
        if (visited.contains(node)) {
            return true;
        }
        
        visiting.add(node);
        
        for (GraphNode dependency : node.getDependencies()) {
            if (!topologicalSortDFS(dependency, visited, visiting, result)) {
                return false;
            }
        }
        
        visiting.remove(node);
        visited.add(node);
        result.add(node);
        
        return true;
    }
    
    private List<GraphNode> getTopologicalOrderWithCycleBreaking() {
        // Simple cycle breaking: remove edges with lowest priority
        logger.info("Breaking cycles in dependency graph");
        
        List<GraphNode> result = new ArrayList<>();
        Set<GraphNode> visited = new HashSet<>();
        
        // Use Kahn's algorithm with priority queue
        Queue<GraphNode> queue = new PriorityQueue<>((a, b) -> 
            Integer.compare(b.calculateMigrationPriority(), a.calculateMigrationPriority()));
        
        // Calculate in-degrees
        Map<GraphNode, Integer> inDegree = new HashMap<>();
        for (GraphNode node : nodes.values()) {
            inDegree.put(node, node.getDependencies().size());
            if (node.getDependencies().isEmpty()) {
                queue.offer(node);
            }
        }
        
        while (!queue.isEmpty()) {
            GraphNode current = queue.poll();
            result.add(current);
            visited.add(current);
            
            for (GraphNode dependent : current.getDependents()) {
                int newInDegree = inDegree.get(dependent) - 1;
                inDegree.put(dependent, newInDegree);
                
                if (newInDegree == 0) {
                    queue.offer(dependent);
                }
            }
        }
        
        return result;
    }
    
    private void detectCyclesDFS(GraphNode node, Set<GraphNode> visited, Set<GraphNode> visiting,
                               List<GraphNode> path, List<List<GraphNode>> cycles) {
        if (visiting.contains(node)) {
            // Found a cycle
            int cycleStart = path.indexOf(node);
            if (cycleStart >= 0) {
                cycles.add(new ArrayList<>(path.subList(cycleStart, path.size())));
            }
            return;
        }
        
        if (visited.contains(node)) {
            return;
        }
        
        visiting.add(node);
        path.add(node);
        
        for (GraphNode dependency : node.getDependencies()) {
            detectCyclesDFS(dependency, visited, visiting, path, cycles);
        }
        
        path.remove(path.size() - 1);
        visiting.remove(node);
        visited.add(node);
    }
    
    private void calculateDepthDFS(GraphNode node, int depth) {
        if (node.getDepth() >= depth) {
            return; // Already calculated with higher or equal depth
        }
        
        node.setDepth(depth);
        
        for (GraphNode dependent : node.getDependents()) {
            calculateDepthDFS(dependent, depth + 1);
        }
    }
    
    /**
     * Graph statistics container
     */
    public static class GraphStatistics {
        public int totalNodes;
        public int rootNodes;
        public int leafNodes;
        public int maxDepth;
        public int circularDependencies;
        public Map<ComponentModel.ComponentType, Integer> componentTypeCounts = new HashMap<>();
        
        @Override
        public String toString() {
            return String.format(
                "GraphStatistics{totalNodes=%d, rootNodes=%d, leafNodes=%d, maxDepth=%d, circularDependencies=%d, componentTypes=%s}",
                totalNodes, rootNodes, leafNodes, maxDepth, circularDependencies, componentTypeCounts
            );
        }
    }
}
