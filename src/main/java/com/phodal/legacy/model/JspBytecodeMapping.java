package com.phodal.legacy.model;

import java.util.*;

/**
 * Represents the mapping relationship between JSP files and their compiled bytecode.
 * This class stores the connections between JSP source code and the corresponding
 * compiled servlet classes, methods, and dependencies.
 */
public class JspBytecodeMapping {
    
    private String jspComponentId;
    private String jspPath;
    private String compiledServletClass;
    private Map<String, String> classNameMappings;
    private Map<String, MethodMapping> methodMappings;
    private Map<String, String> dependencyMappings;
    private Set<String> tagLibraryMappings;
    private Map<String, Object> metadata;
    private long createdTime;
    
    public JspBytecodeMapping(String jspComponentId, String jspPath) {
        this.jspComponentId = jspComponentId;
        this.jspPath = jspPath;
        this.classNameMappings = new HashMap<>();
        this.methodMappings = new HashMap<>();
        this.dependencyMappings = new HashMap<>();
        this.tagLibraryMappings = new HashSet<>();
        this.metadata = new HashMap<>();
        this.createdTime = System.currentTimeMillis();
    }
    
    // Getters and setters
    public String getJspComponentId() { return jspComponentId; }
    public void setJspComponentId(String jspComponentId) { this.jspComponentId = jspComponentId; }
    
    public String getJspPath() { return jspPath; }
    public void setJspPath(String jspPath) { this.jspPath = jspPath; }
    
    public String getCompiledServletClass() { return compiledServletClass; }
    public void setCompiledServletClass(String compiledServletClass) { this.compiledServletClass = compiledServletClass; }
    
    public Map<String, String> getClassNameMappings() { return classNameMappings; }
    public void setClassNameMappings(Map<String, String> classNameMappings) { this.classNameMappings = classNameMappings; }
    
    public Map<String, MethodMapping> getMethodMappings() { return methodMappings; }
    public void setMethodMappings(Map<String, MethodMapping> methodMappings) { this.methodMappings = methodMappings; }
    
    public Map<String, String> getDependencyMappings() { return dependencyMappings; }
    public void setDependencyMappings(Map<String, String> dependencyMappings) { this.dependencyMappings = dependencyMappings; }
    
    public Set<String> getTagLibraryMappings() { return tagLibraryMappings; }
    public void setTagLibraryMappings(Set<String> tagLibraryMappings) { this.tagLibraryMappings = tagLibraryMappings; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public long getCreatedTime() { return createdTime; }
    public void setCreatedTime(long createdTime) { this.createdTime = createdTime; }
    
    // Utility methods
    public void addClassMapping(String jspClassName, String bytecodeClassName) {
        this.classNameMappings.put(jspClassName, bytecodeClassName);
    }
    
    public void addMethodMapping(String jspMethodSignature, String bytecodeMethodSignature, 
                                int jspLineNumber, String methodType) {
        MethodMapping mapping = new MethodMapping(jspMethodSignature, bytecodeMethodSignature, 
                                                 jspLineNumber, methodType);
        this.methodMappings.put(jspMethodSignature, mapping);
    }
    
    public void addDependencyMapping(String jspDependency, String bytecodeDependency) {
        this.dependencyMappings.put(jspDependency, bytecodeDependency);
    }
    
    public void addTagLibraryMapping(String tagLibraryUri) {
        this.tagLibraryMappings.add(tagLibraryUri);
    }
    
    public void addMetadata(String key, Object value) {
        this.metadata.put(key, value);
    }
    
    public boolean hasClassMapping(String jspClassName) {
        return this.classNameMappings.containsKey(jspClassName);
    }
    
    public boolean hasMethodMapping(String jspMethodSignature) {
        return this.methodMappings.containsKey(jspMethodSignature);
    }
    
    public String getBytecodeClass(String jspClassName) {
        return this.classNameMappings.get(jspClassName);
    }
    
    public MethodMapping getMethodMapping(String jspMethodSignature) {
        return this.methodMappings.get(jspMethodSignature);
    }
    
    public String getBytecodeDependency(String jspDependency) {
        return this.dependencyMappings.get(jspDependency);
    }
    
    @Override
    public String toString() {
        return String.format("JspBytecodeMapping{jspPath='%s', compiledServletClass='%s', " +
                           "classMappings=%d, methodMappings=%d, dependencyMappings=%d}",
                           jspPath, compiledServletClass, classNameMappings.size(), 
                           methodMappings.size(), dependencyMappings.size());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JspBytecodeMapping that = (JspBytecodeMapping) o;
        return Objects.equals(jspComponentId, that.jspComponentId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(jspComponentId);
    }
    
    /**
     * Represents a mapping between JSP method/code block and bytecode method
     */
    public static class MethodMapping {
        private String jspMethodSignature;
        private String bytecodeMethodSignature;
        private int jspLineNumber;
        private String methodType; // scriptlet, expression, declaration, etc.
        private Map<String, Object> attributes;
        
        public MethodMapping(String jspMethodSignature, String bytecodeMethodSignature, 
                           int jspLineNumber, String methodType) {
            this.jspMethodSignature = jspMethodSignature;
            this.bytecodeMethodSignature = bytecodeMethodSignature;
            this.jspLineNumber = jspLineNumber;
            this.methodType = methodType;
            this.attributes = new HashMap<>();
        }
        
        // Getters and setters
        public String getJspMethodSignature() { return jspMethodSignature; }
        public void setJspMethodSignature(String jspMethodSignature) { this.jspMethodSignature = jspMethodSignature; }
        
        public String getBytecodeMethodSignature() { return bytecodeMethodSignature; }
        public void setBytecodeMethodSignature(String bytecodeMethodSignature) { this.bytecodeMethodSignature = bytecodeMethodSignature; }
        
        public int getJspLineNumber() { return jspLineNumber; }
        public void setJspLineNumber(int jspLineNumber) { this.jspLineNumber = jspLineNumber; }
        
        public String getMethodType() { return methodType; }
        public void setMethodType(String methodType) { this.methodType = methodType; }
        
        public Map<String, Object> getAttributes() { return attributes; }
        public void setAttributes(Map<String, Object> attributes) { this.attributes = attributes; }
        
        public void addAttribute(String key, Object value) {
            this.attributes.put(key, value);
        }
        
        @Override
        public String toString() {
            return String.format("MethodMapping{jspMethod='%s', bytecodeMethod='%s', line=%d, type='%s'}",
                               jspMethodSignature, bytecodeMethodSignature, jspLineNumber, methodType);
        }
    }
}
