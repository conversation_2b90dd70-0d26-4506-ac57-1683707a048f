---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.legacy.modernize.JavaModernization
displayName: Java Modernization for Legacy Projects
description: Comprehensive Java modernization including JDK upgrades and Spring Boot migration
tags:
  - java
  - spring-boot
  - modernization
  - migration

recipeList:
  # Java version upgrades
  - org.openrewrite.java.migrate.Java8toJava11
  - org.openrewrite.java.migrate.Java11toJava17
  - org.openrewrite.java.migrate.Java17toJava21
  
  # Spring Boot upgrades
  - org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_2
  - org.openrewrite.java.spring.boot2.SpringBoot2JUnit4to5Migration
  - org.openrewrite.java.spring.boot2.MigrateSpringBootProperties_2_4
  
  # Code modernization
  - org.openrewrite.java.cleanup.CommonDeclarationSiteTypeVariances
  - org.openrewrite.java.cleanup.CommonStaticAnalysis
  - org.openrewrite.java.cleanup.UnnecessaryParentheses
  - org.openrewrite.java.cleanup.RemoveUnusedImports
  - org.openrewrite.java.cleanup.RemoveUnusedLocalVariables
  - org.openrewrite.java.cleanup.SimplifyBooleanExpression
  - org.openrewrite.java.cleanup.SimplifyBooleanReturn
  - org.openrewrite.java.cleanup.UseCollectionInterfaces
  - org.openrewrite.java.cleanup.UseDiamondOperator
  - org.openrewrite.java.cleanup.UseStringReplace
  
  # JUnit 4 to 5 migration
  - org.openrewrite.java.testing.junit5.JUnit4to5Migration
  - org.openrewrite.java.testing.junit5.AssertToAssertions
  - org.openrewrite.java.testing.junit5.CategoryToTag
  - org.openrewrite.java.testing.junit5.CleanupJUnit5
  
  # Maven/Gradle modernization
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-compiler-plugin
      newVersion: 3.11.0
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-surefire-plugin
      newVersion: 3.2.2
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.springframework.boot
      artifactId: spring-boot-maven-plugin
      newVersion: 3.2.1
      
  # Code formatting
  - org.openrewrite.java.format.AutoFormat
  - org.openrewrite.java.format.BlankLines
  - org.openrewrite.java.format.EmptyNewlineAtEndOfFile
  - org.openrewrite.java.format.NormalizeFormat
  - org.openrewrite.java.format.RemoveTrailingWhitespace

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.legacy.modernize.JDKUpgrade
displayName: JDK Version Upgrade
description: Upgrade Java version with syntax modernization
tags:
  - java
  - jdk
  - upgrade

recipeList:
  # Java 8 to 11 migration
  - org.openrewrite.java.migrate.Java8toJava11
  - org.openrewrite.java.migrate.javax.JavaxMigrationToJakarta
  
  # Java 11 to 17 migration  
  - org.openrewrite.java.migrate.Java11toJava17
  - org.openrewrite.java.migrate.RemovedPolicy
  - org.openrewrite.java.migrate.RemovedToolProviderConstructor
  
  # Java 17 to 21 migration
  - org.openrewrite.java.migrate.Java17toJava21
  - org.openrewrite.java.migrate.RemovedLegacyApis
  
  # Modern Java features
  - org.openrewrite.java.cleanup.UseDiamondOperator
  - org.openrewrite.java.cleanup.UseCollectionInterfaces
  - org.openrewrite.java.cleanup.UseStringReplace
  - org.openrewrite.java.cleanup.SimplifyBooleanExpression
  - org.openrewrite.java.cleanup.SimplifyBooleanReturn

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.legacy.modernize.SpringBootUpgrade
displayName: Spring Boot Upgrade
description: Upgrade Spring Boot to latest version with configuration migration
tags:
  - spring-boot
  - upgrade
  - migration

recipeList:
  # Spring Boot 2.x to 3.x migration
  - org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_2
  - org.openrewrite.java.spring.boot3.ConfigurationOverPropertiesFile
  - org.openrewrite.java.spring.boot3.MigrateThymeleafDependencies
  - org.openrewrite.java.spring.boot3.RemoveConstructorBindingAnnotation
  
  # Spring Framework upgrades
  - org.openrewrite.java.spring.framework.MigrateResponseStatusException
  - org.openrewrite.java.spring.framework.UpgradeSpringFramework_6_0
  
  # Security upgrades
  - org.openrewrite.java.spring.security6.UpgradeSpringSecurity_6_0
  - org.openrewrite.java.spring.security6.RequireExplicitSavingOfSecurityContextRepository

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.legacy.modernize.TestModernization
displayName: Test Code Modernization
description: Modernize test code including JUnit 4 to 5 migration
tags:
  - testing
  - junit
  - modernization

recipeList:
  # JUnit 4 to 5 migration
  - org.openrewrite.java.testing.junit5.JUnit4to5Migration
  - org.openrewrite.java.testing.junit5.AssertToAssertions
  - org.openrewrite.java.testing.junit5.CategoryToTag
  - org.openrewrite.java.testing.junit5.ExpectedExceptionToAssertThrows
  - org.openrewrite.java.testing.junit5.JUnit5BestPractices
  - org.openrewrite.java.testing.junit5.CleanupJUnit5
  
  # Mockito upgrades
  - org.openrewrite.java.testing.mockito.Mockito1to4Migration
  - org.openrewrite.java.testing.mockito.MockitoJUnitToMockitoExtension
  
  # AssertJ improvements
  - org.openrewrite.java.testing.assertj.JUnitToAssertj
  - org.openrewrite.java.testing.assertj.SimplifyChainedAssertJAssertions

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.legacy.modernize.DependencyUpgrade
displayName: Dependency Version Upgrade
description: Upgrade Maven and Gradle dependencies to latest versions
tags:
  - dependencies
  - maven
  - gradle
  - upgrade

recipeList:
  # Maven plugin upgrades
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-compiler-plugin
      newVersion: 3.11.0
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-surefire-plugin
      newVersion: 3.2.2
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-failsafe-plugin
      newVersion: 3.2.2
  - org.openrewrite.maven.UpgradePluginVersion:
      groupId: org.apache.maven.plugins
      artifactId: maven-shade-plugin
      newVersion: 3.5.1
      
  # Gradle plugin upgrades
  - org.openrewrite.gradle.UpdateGradleWrapper:
      version: 8.5
  - org.openrewrite.gradle.plugins.UpgradePluginVersion:
      pluginIdPattern: org.springframework.boot
      newVersion: 3.2.1
  - org.openrewrite.gradle.plugins.UpgradePluginVersion:
      pluginIdPattern: io.spring.dependency-management
      newVersion: 1.1.4
      
  # Common dependency upgrades
  - org.openrewrite.maven.UpgradeDependencyVersion:
      groupId: org.junit.jupiter
      artifactId: junit-jupiter
      newVersion: 5.10.1
  - org.openrewrite.maven.UpgradeDependencyVersion:
      groupId: org.mockito
      artifactId: mockito-core
      newVersion: 5.8.0
  - org.openrewrite.maven.UpgradeDependencyVersion:
      groupId: org.assertj
      artifactId: assertj-core
      newVersion: 3.25.1
